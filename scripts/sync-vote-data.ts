#!/usr/bin/env tsx

/**
 * <PERSON>ript to sync vote data and ensure consistency between user_votes and product_votes tables
 * This script will:
 * 1. Reset product_votes table to reflect actual user votes
 * 2. Ensure data consistency
 * 3. Verify the sync was successful
 */

import { createSupabaseServerClient } from "@/lib/supabase/client"

async function syncVoteData() {
  console.log("🔄 开始同步投票数据...")

  try {
    const supabase = createSupabaseServerClient()

    // Step 1: Get actual vote counts from user_votes
    console.log("📊 计算实际投票数...")
    const { data: actualVotes, error: votesError } = await supabase
      .from('user_votes')
      .select('product_id, vote_type')
    
    if (votesError) {
      throw new Error(`获取用户投票失败: ${votesError.message}`)
    }

    // Calculate vote counts per product
    const voteCounts = new Map<number, { upvotes: number, downvotes: number }>()
    
    actualVotes?.forEach(vote => {
      const current = voteCounts.get(vote.product_id) || { upvotes: 0, downvotes: 0 }
      if (vote.vote_type === 'upvote') {
        current.upvotes++
      } else if (vote.vote_type === 'downvote') {
        current.downvotes++
      }
      voteCounts.set(vote.product_id, current)
    })

    console.log(`📈 发现 ${voteCounts.size} 个产品有实际投票`)

    // Step 2: Clear existing product_votes data
    console.log("🗑️  清理现有投票计数数据...")
    const { error: deleteError } = await supabase
      .from('product_votes')
      .delete()
      .neq('product_id', 0) // Delete all records

    if (deleteError) {
      throw new Error(`清理数据失败: ${deleteError.message}`)
    }

    // Step 3: Insert correct vote counts
    if (voteCounts.size > 0) {
      console.log("📝 插入正确的投票计数...")
      
      const insertData = Array.from(voteCounts.entries()).map(([productId, counts]) => ({
        product_id: productId,
        upvotes: counts.upvotes,
        downvotes: counts.downvotes
      }))

      const { error: insertError } = await supabase
        .from('product_votes')
        .insert(insertData)

      if (insertError) {
        throw new Error(`插入数据失败: ${insertError.message}`)
      }

      console.log(`✅ 成功同步 ${insertData.length} 个产品的投票数据`)
    } else {
      console.log("ℹ️  没有需要同步的投票数据")
    }

    // Step 4: Verify consistency
    console.log("🔍 验证数据一致性...")
    const { data: consistencyCheck, error: checkError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT 
          pv.product_id,
          pv.upvotes as stored_upvotes,
          COUNT(CASE WHEN uv.vote_type = 'upvote' THEN 1 END) as actual_upvotes,
          pv.downvotes as stored_downvotes,
          COUNT(CASE WHEN uv.vote_type = 'downvote' THEN 1 END) as actual_downvotes,
          (pv.upvotes = COUNT(CASE WHEN uv.vote_type = 'upvote' THEN 1 END) AND 
           pv.downvotes = COUNT(CASE WHEN uv.vote_type = 'downvote' THEN 1 END)) as is_consistent
        FROM product_votes pv
        LEFT JOIN user_votes uv ON uv.product_id = pv.product_id
        GROUP BY pv.product_id, pv.upvotes, pv.downvotes
        ORDER BY pv.product_id;
      `
    })

    if (checkError) {
      console.warn("⚠️  无法验证一致性:", checkError.message)
    } else {
      const inconsistentCount = consistencyCheck?.filter((row: any) => !row.is_consistent).length || 0
      if (inconsistentCount === 0) {
        console.log("✅ 数据一致性验证通过！")
      } else {
        console.warn(`⚠️  发现 ${inconsistentCount} 个不一致的记录`)
      }
    }

    // Step 5: Show summary
    console.log("\n📋 同步总结:")
    console.log(`- 处理的产品数量: ${voteCounts.size}`)
    console.log(`- 总投票数: ${actualVotes?.length || 0}`)
    
    voteCounts.forEach((counts, productId) => {
      console.log(`  产品 ${productId}: ${counts.upvotes} 个赞, ${counts.downvotes} 个踩`)
    })

  } catch (error) {
    console.error("❌ 同步过程中发生错误:", error)
    process.exit(1)
  }
}

// 运行同步
syncVoteData()
  .then(() => {
    console.log("\n🎉 投票数据同步完成！")
    process.exit(0)
  })
  .catch((error) => {
    console.error("💥 同步过程中发生未处理的错误:", error)
    process.exit(1)
  })
