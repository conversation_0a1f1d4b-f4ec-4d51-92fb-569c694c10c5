#!/usr/bin/env tsx

/**
 * <PERSON><PERSON>t to run the voting tables migration
 * This script will create the necessary tables for the voting functionality
 */

import { createSupabaseServerClient } from "@/lib/supabase/client"
import { readFileSync } from "fs"
import { join } from "path"

async function runVotingMigration() {
  console.log("🚀 开始运行投票功能数据库迁移...")

  try {
    const supabase = createSupabaseServerClient()

    // 读取迁移文件
    const migrationPath = join(process.cwd(), "supabase/migrations/20250101_create_voting_tables.sql")
    const migrationSQL = readFileSync(migrationPath, "utf-8")

    console.log("📄 读取迁移文件:", migrationPath)

    // 执行迁移
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL })

    if (error) {
      console.error("❌ 迁移失败:", error)
      process.exit(1)
    }

    console.log("✅ 投票功能数据库迁移成功完成!")
    console.log("📊 已创建以下表:")
    console.log("  - product_votes: 存储产品投票计数")
    console.log("  - user_votes: 存储用户投票记录")
    console.log("🔒 已设置行级安全策略 (RLS)")
    console.log("⚡ 已创建自动更新投票计数的触发器")

    // 验证表是否创建成功
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['product_votes', 'user_votes'])

    if (tablesError) {
      console.warn("⚠️  无法验证表创建状态:", tablesError)
    } else {
      console.log("🔍 验证结果:")
      tables?.forEach(table => {
        console.log(`  ✓ ${table.table_name} 表已创建`)
      })
    }

  } catch (error) {
    console.error("❌ 运行迁移时发生错误:", error)
    process.exit(1)
  }
}

// 运行迁移
runVotingMigration()
  .then(() => {
    console.log("🎉 迁移完成，投票功能现在可以正常使用了!")
    process.exit(0)
  })
  .catch((error) => {
    console.error("💥 迁移过程中发生未处理的错误:", error)
    process.exit(1)
  })
