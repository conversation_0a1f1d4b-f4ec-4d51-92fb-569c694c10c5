"use client"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { prefetchProductDataClient } from "@/lib/supabase/db"
import type { Product } from "@/lib/supabase/types"

interface ProductPrefetcherProps {
  products: Product[]
}

export function ProductPrefetcher({ products }: ProductPrefetcherProps) {
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // 只在首页预加载
    if (pathname !== "/") return

    // 预加载前几个产品的数据
    const prefetchTopProducts = async () => {
      // 只预加载前6个产品以避免过多请求
      const topProducts = products.slice(0, 6)

      // 使用 Promise.all 并行预加载
      await Promise.all(
        topProducts.map((product) => {
          // 预获取路由
          router.prefetch(`/product/${product.slug}`)
          // 使用客户端版本的预加载函数
          return prefetchProductDataClient(product.id)
        }),
      )
    }

    // 立即开始预加载，不等待空闲时间
    prefetchTopProducts()
  }, [products, router, pathname])

  // 这个组件不渲染任何内容
  return null
}
