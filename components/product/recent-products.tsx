import { TinyCard } from "@/components/product/cards/tiny-card"
import type { Product } from "@/lib/supabase/types"

interface RecentProductsProps {
  products: Product[]
}

// Fix the centering issue by removing the right padding and ensuring consistent padding
export function RecentProducts({ products }: RecentProductsProps) {
  return (
    <div className="w-full py-2 md:py-4 flex justify-center">
      <div className="flex items-center gap-2 md:gap-3 overflow-x-auto pb-2 md:pb-4 scrollbar-hide px-4 md:px-0 mx-auto">
        <div className="text-xs md:text-sm font-medium whitespace-nowrap flex-shrink-0">New Arrival:</div>
        <div className="flex items-center gap-2 overflow-x-auto scrollbar-hide">
          {products.map((product) => (
            <TinyCard key={product.id} product={product} />
          ))}
        </div>
      </div>
    </div>
  )
}
