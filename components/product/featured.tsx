import React from 'react';
import { SmallCard } from '@/components/product/cards/small-card';
import type { Product } from '@/lib/supabase/types';

interface FeaturedProps {
  products?: Product[];
}

export function Featured({ products }: FeaturedProps) {
  return (
    <div className="p-6 rounded-2xl bg-gray-50">
      <h2 className="text-lg font-semibold mb-4">Featured</h2>
      {products && products.length > 0 ? (
        <div className="">
          {products.map((product, index) => (
            <div key={product.id} className={index !== products.length - 1 ? "mb-6" : ""}>
              <SmallCard product={product} />
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-600">No featured items at the moment.</p>
      )}
    </div>
  );
}
