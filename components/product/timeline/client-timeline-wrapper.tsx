"use client"

import { useState, useEffect } from "react"
import { ProductTimeline } from "@/components/product/timeline/product-timeline"
import type { Product } from "@/lib/supabase/types"

interface ClientTimelineWrapperProps {
  initialProducts: Product[]
}

export function ClientTimelineWrapper({ initialProducts }: ClientTimelineWrapperProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // On the server or during initial hydration, use the passed products
  if (!isClient) {
    return <ProductTimeline products={initialProducts} />
  }

  // After hydration, we can use any client-side features if needed
  return <ProductTimeline products={initialProducts} />
}
