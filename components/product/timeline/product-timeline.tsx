import { BigCard } from "@/components/product/cards/big-card"
import { useState } from "react"
import type { Product } from "@/lib/supabase/types"
import { formatDate } from "@/lib/utils"

interface ProductTimelineProps {
  products: Product[]
}

export function ProductTimeline({ products }: ProductTimelineProps) {
  // Group products by date
  const productsByDate: Record<string, Product[]> = {}

  products.forEach((product) => {
    const date = new Date(product.createdAt).toISOString().split("T")[0]
    if (!productsByDate[date]) {
      productsByDate[date] = []
    }
    productsByDate[date].push(product)
  })

  // Sort dates in descending order (newest first)
  const dates = Object.keys(productsByDate).sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
  // 分页：每页显示 7 天
  const [page, setPage] = useState(1)
  const pageSize = 7
  const visibleDates = dates.slice(0, page * pageSize)

  return (
    <div className="space-y-12">
      {visibleDates.map((date) => (
        <div key={date} className="space-y-6">
          <div className="flex items-center gap-4">
            <h2 className="text-sm font-medium whitespace-nowrap">{formatDate(date)}</h2>
            <div className="border-t border-dashed border-gray-200 flex-grow"></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {productsByDate[date]?.map((product) => (
              <BigCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      ))}
      {visibleDates.length < dates.length && (
        <div className="mt-6">
          <button onClick={() => setPage(page + 1)} className="block w-full px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
            Load more
          </button>
        </div>
      )}
    </div>
  )
}
