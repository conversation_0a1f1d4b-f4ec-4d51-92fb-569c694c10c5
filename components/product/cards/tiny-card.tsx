import Image from "next/image"
import Link from "next/link"
import type { Product } from "@/lib/supabase/types"

interface TinyCardProps {
  product: Product
}

export function TinyCard({ product }: TinyCardProps) {
  return (
    <Link href={`/product/${product.slug}`} className="flex-shrink-0">
      <div className="flex items-center gap-1 md:gap-1.5 px-2 md:px-3 py-1 md:py-1.5 bg-white rounded-full border hover:shadow-sm transition-shadow">
        <div className="w-4 h-4 md:w-5 md:h-5 rounded-full overflow-hidden flex-shrink-0 relative">
          <Image
            src={product.logoUrl || "/placeholder.svg"}
            alt={`${product.name} logo`}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 16px, 20px"
            placeholder="blur"
            blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEtAJJXIDTjwAAAABJRU5ErkJggg=="
            quality={90}
          />
        </div>
        <span className="whitespace-nowrap text-xs max-w-[80px] md:max-w-none truncate">{product.name}</span>
      </div>
    </Link>
  )
}
