import Link from "next/link"

interface CollectionCardProps {
  title: string
  subtitle: string
  slug?: string
  coverImages?: string[] // 集合中产品的封面图链接数组
}

export default function CollectionCard({ title, subtitle, slug, coverImages = [] }: CollectionCardProps) {
  // 根据集合标题生成默认的 slug
  const defaultSlug = title.toLowerCase().replace(/\s+/g, "-")
  const collectionSlug = slug || defaultSlug

  const card = (
    <div className="flex flex-col">
      <div className="overflow-hidden transition-all hover:shadow-md mb-3 border rounded-md">
        <div className="relative w-full aspect-[16/9] overflow-hidden group">
          {/* Stacked cards */}
          <div className="absolute inset-x-8 top-8 bottom-[-20%]">
            {/* Bottom card */}
            <div
              className="absolute inset-x-0 aspect-[16/9] w-[82%] mx-auto top-2 rounded-xl transform transition-all duration-300 group-hover:-translate-x-6 group-hover:-rotate-6 overflow-hidden border shadow-md z-0"
              style={{
                backgroundImage: `url('${coverImages[0] || "/covers/default-cover.png"}')`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                filter: "brightness(1.1) contrast(0.9)",
                backgroundColor: "#f4f4f5",
              }}
            />

            {/* Middle card */}
            <div
              className="absolute inset-x-0 aspect-[16/9] w-[88%] mx-auto top-3 rounded-xl transform transition-all duration-300 group-hover:translate-x-6 group-hover:rotate-6 overflow-hidden border shadow-md z-10"
              style={{
                backgroundImage: `url('${coverImages[1] || coverImages[0] || "/covers/default-cover.png"}')`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                filter: "brightness(1.05) contrast(0.95)",
                backgroundColor: "#f4f4f5",
              }}
            />

            {/* Top card */}
            <div
              className="absolute inset-x-0 aspect-[16/9] w-[95%] mx-auto top-5 rounded-xl transform transition-all duration-300 group-hover:translate-y-6 overflow-hidden border shadow-md z-20"
              style={{
                backgroundImage: `url('${coverImages[2] || coverImages[1] || coverImages[0] || "/covers/default-cover.png"}')`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundColor: "#f4f4f5",
              }}
            />
          </div>
        </div>
      </div>
      <div className="px-1">
        <h2 className="text-lg font-medium">{title}</h2>
        <p className="text-sm text-muted-foreground mt-1">{subtitle}</p>
      </div>
    </div>
  )

  // 如果有 slug，则添加链接
  return slug ? (
    <Link href={`/collections/${collectionSlug}`} className="block">
      {card}
    </Link>
  ) : (
    card
  )
}
