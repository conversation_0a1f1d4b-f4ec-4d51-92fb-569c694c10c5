"use client"

import Image from "next/image"
import Link from "next/link"
import { Card } from "@/components/ui/card"
import type { Product } from "@/lib/supabase/types"
import { useCallback, useState } from "react"
import { useRouter } from "next/navigation"
import { IconBadge } from "@/components/common/icon-badge"
import { productBadgesConfig } from "@/lib/config/site-config"
import { BadgeType } from "@/components/common/icon-badge"
import { Vote } from "@/components/product/vote"

interface BigCardProps {
  product: Product
}

export function BigCard({ product }: BigCardProps) {
  const router = useRouter()
  const [isPrefetching, setIsPrefetching] = useState(false)

  // 鼠标悬停时预加载产品详情页
  const handleMouseEnter = useCallback(() => {
    if (!isPrefetching) {
      setIsPrefetching(true)
      // 预取产品详情页路由，只使用 slug
      router.prefetch(`/product/${product.slug}`)
    }
  }, [router, product.slug, isPrefetching])

  // 检查产品是否应该显示徽章，以及显示哪种徽章
  const getBadgeType = useCallback((): BadgeType | null => {
    const productId = product.id

    // 检查产品是否在各种徽章配置中
    for (const [badgeType, productIds] of Object.entries(productBadgesConfig)) {
      if (productIds.includes(productId)) {
        return badgeType as BadgeType
      }
    }

    return null
  }, [product.id])

  const badgeType = getBadgeType()

  return (
    <div className="flex flex-col" onMouseEnter={handleMouseEnter}>
      <Link href={`/product/${product.slug}`} prefetch={true}>
        <Card className="overflow-hidden transition-all hover:shadow-md mb-3">
          <div className="relative aspect-[16/9] w-full">
            <Image
              src={product.coverImageUrl || "/placeholder.svg"}
              alt={product.name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              placeholder="blur"
              blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEtAJJXIDTjwAAAABJRU5ErkJggg=="
              quality={85}
            />
          </div>
        </Card>
      </Link>
      <div className="px-1">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">{product.name}</h3>
          {badgeType && (
            <IconBadge type={badgeType} size="sm" showLabel={true} className="mt-0.5" />
          )}
        </div>
        <p className="text-sm text-muted-foreground mt-1">{product.tagline}</p>
      </div>
    </div>
  )
}
