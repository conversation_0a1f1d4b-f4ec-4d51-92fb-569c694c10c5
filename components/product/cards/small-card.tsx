import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import type { Product } from "@/lib/supabase/types"
import { IconBadge } from "@/components/common/icon-badge"
import { productBadgesConfig } from "@/lib/config/site-config"
import { BadgeType } from "@/components/common/icon-badge"
import { useCallback } from "react"

interface SmallCardProps {
  product: Product
}

export function SmallCard({ product }: SmallCardProps) {
  // 检查产品是否应该显示徽章，以及显示哪种徽章
  const getBadgeType = useCallback((): BadgeType | null => {
    const productId = product.id

    // 检查产品是否在各种徽章配置中
    for (const [badgeType, productIds] of Object.entries(productBadgesConfig)) {
      if (productIds.includes(productId)) {
        return badgeType as BadgeType
      }
    }

    return null
  }, [product.id])

  const badgeType = getBadgeType()

  return (
    <Link href={`/product/${product.slug}`}>
      <Card className="overflow-hidden transition-all border border-gray-200 rounded-2xl hover:bg-gray-50 shadow-none">
        <CardContent className="p-3 flex items-center gap-3">
          <div className="relative h-12 w-12 flex-shrink-0 bg-gray-100 rounded-xl overflow-hidden">
            <Image
              src={product.logoUrl || "/placeholder.svg"}
              alt={`${product.name} logo`}
              fill
              className="object-contain p-2"
              sizes="48px"
              placeholder="blur"
              blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEtAJJXIDTjwAAAABJRU5ErkJggg=="
              quality={90}
            />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1.5">
              <h3 className="font-medium text-sm">{product.name}</h3>
              {badgeType && (
                <IconBadge type={badgeType} size="sm" showLabel={false} className="bg-transparent border-0 px-0 rounded-none" />
              )}
            </div>
            <p className="text-xs text-muted-foreground line-clamp-1">{product.tagline}</p>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
