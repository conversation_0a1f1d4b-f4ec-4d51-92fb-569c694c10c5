import { RecentProducts } from "@/components/product/recent-products"
import type { Product } from "@/lib/supabase/types"

interface HeroProps {
  recentProducts: Product[]
}

export function Hero({ recentProducts }: HeroProps) {
  return (
    <section className="pt-12 pb-2 md:pt-20 md:pb-4">
      <div className="container px-4 md:px-6 max-w-5xl mx-auto">
        <div className="flex flex-col items-center text-center space-y-4">
          <h1 className="text-3xl md:text-5xl font-bold tracking-tighter font-outfit">
            Every day is an{" "}
            <span className="bg-gradient-to-r from-teal-400 via-green-400 to-lime-400 bg-clip-text text-transparent">
              introducing.day
            </span>
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl">
            Discover amazing products from top creators around the world every day.
          </p>

          <div className="w-full mt-6 -mx-4">
            <RecentProducts products={recentProducts} />
          </div>
        </div>
      </div>
    </section>
  )
}
