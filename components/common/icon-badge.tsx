"use client"

import { cn } from "@/lib/utils"
import { <PERSON>ge<PERSON>heck, CheckCircle, Award, Star, Shield, Zap } from "lucide-react"

export type BadgeType = "verified" | "sponsor" | "featured" | "trusted" | "new" | string

interface IconBadgeProps {
  type: BadgeType
  className?: string
  size?: "sm" | "md" | "lg"
  showLabel?: boolean
  customLabel?: string
}

const iconMap = {
  verified: BadgeCheck,
  sponsor: Award,
  featured: Star,
  trusted: Shield,
  new: Zap,
}

const colorMap = {
  verified: "bg-blue-50 text-blue-600 border-blue-200",
  sponsor: "bg-purple-50 text-purple-600 border-purple-200",
  featured: "bg-amber-50 text-amber-600 border-amber-200",
  trusted: "bg-green-50 text-green-600 border-green-200",
  new: "bg-rose-50 text-rose-600 border-rose-200",
}

const labelMap = {
  verified: "Verified",
  sponsor: "Sponsor",
  featured: "Featured",
  trusted: "Trusted",
  new: "New",
}

export function IconBadge({ 
  type, 
  className, 
  size = "sm", 
  showLabel = false,
  customLabel
}: IconBadgeProps) {
  // Get the appropriate icon component
  const IconComponent = iconMap[type as keyof typeof iconMap] || CheckCircle
  
  // Get the appropriate color scheme
  const colorClass = colorMap[type as keyof typeof colorMap] || "bg-gray-50 text-gray-600 border-gray-200"
  
  // Get the appropriate label text
  const label = customLabel || labelMap[type as keyof typeof labelMap] || type

  // Size classes
  const sizeClasses = {
    sm: "h-5 text-xs",
    md: "h-6 text-sm",
    lg: "h-7 text-sm",
  }
  
  const iconSizes = {
    sm: 12,
    md: 14,
    lg: 16,
  }

  return (
    <div 
      className={cn(
        "inline-flex items-center rounded-full border px-2 font-medium",
        colorClass,
        sizeClasses[size],
        className
      )}
    >
      <IconComponent size={iconSizes[size]} className="mr-1" />
      {showLabel && <span>{label}</span>}
    </div>
  )
}
