"use client"

import { useEffect, useState } from "react"

interface CountdownProps {
  // Optional targetDate for backward compatibility
  targetDate?: string
}

export function Countdown({ targetDate }: CountdownProps) {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0,
  })
  const [showNotice, setShowNotice] = useState(false)

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date()

      // 设置目标时间：每天的 16:00 UTC (相当于太平洋时间 8:00 AM)
      const target = new Date()
      target.setUTCHours(16, 0, 0, 0)

      // 如果今天的目标时间已经过了，设置为明天
      if (now.getTime() >= target.getTime()) {
        target.setUTCDate(target.getUTCDate() + 1)
      }

      const differenceMs = target.getTime() - now.getTime()

      if (differenceMs <= 0) {
        return { hours: 0, minutes: 0, seconds: 0 }
      }

      const hours = Math.floor(differenceMs / (1000 * 60 * 60))
      const minutes = Math.floor((differenceMs % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((differenceMs % (1000 * 60)) / 1000)

      return { hours, minutes, seconds }
    }

    const interval = setInterval(() => {
      const time = calculateTimeLeft()
      setTimeLeft(time)
    }, 1000)

    // Initial calculation
    const initialTime = calculateTimeLeft()
    setTimeLeft(initialTime)

    return () => clearInterval(interval)
  }, [])

  // Format the time with leading zeros
  const formattedHours = String(timeLeft.hours).padStart(2, "0")
  const formattedMinutes = String(timeLeft.minutes).padStart(2, "0")
  const formattedSeconds = String(timeLeft.seconds).padStart(2, "0")

  return showNotice ? (
    <div className="text-[14px] text-gray-400 cursor-pointer" onClick={() => setShowNotice(false)}>
      Updates daily at 8 AM PT
    </div>
  ) : (
    <div className="flex items-center gap-3 cursor-pointer" onClick={() => setShowNotice(true)}>
      <div className="text-[14px] text-gray-400 uppercase inline-flex items-center">refresh in</div>
      <div className="seven-segment text-[14px] text-gray-400 inline-flex items-center leading-none" style={{ transform: "translateY(1px)" }}>
        {formattedHours}:{formattedMinutes}:{formattedSeconds}
      </div>
    </div>
  )
}
