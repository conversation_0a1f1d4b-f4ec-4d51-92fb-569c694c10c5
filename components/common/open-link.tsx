"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { Link2 } from "lucide-react"

interface OpenLinkProps {
  productUrl?: string
  productName?: string
  className?: string
}

export function OpenLink({
  productUrl,
  productName = "product",
  className,
}: OpenLinkProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleOpenLink = async () => {
    if (!productUrl || isLoading) return

    setIsLoading(true)

    try {
      // 确保 URL 有正确的协议
      let finalUrl = productUrl
      if (!productUrl.startsWith('http://') && !productUrl.startsWith('https://')) {
        finalUrl = `https://${productUrl}`
      }

      // 在新标签页中打开链接
      window.open(finalUrl, '_blank', 'noopener,noreferrer')
    } catch (error) {
      console.error("打开链接失败:", error)
    } finally {
      // 添加短暂延迟以提供视觉反馈
      setTimeout(() => {
        setIsLoading(false)
      }, 200)
    }
  }

  // 如果没有 URL，不显示组件
  if (!productUrl) {
    return null
  }

  return (
    <div
      role="button"
      tabIndex={isLoading ? -1 : 0}
      aria-label={`Open ${productName} website`}
      title={`Visit ${productName}`}
      className={cn(
        "flex flex-col items-center justify-center border rounded-2xl h-[4.5rem] w-[4.5rem] overflow-hidden transition-all focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "border-gray-200 text-gray-700",
        isLoading
          ? "opacity-50 cursor-not-allowed"
          : "cursor-pointer hover:bg-gray-50 active:bg-gray-100",
        className
      )}
      onClick={!isLoading ? handleOpenLink : undefined}
      onKeyDown={!isLoading ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleOpenLink();
        }
      } : undefined}
    >
      <Link2
        className={cn(
          "h-5 w-5 mb-1 transition-colors",
          "text-muted-foreground"
        )}
      />
      <span
        className={cn(
          "text-sm font-medium transition-colors",
          "text-muted-foreground"
        )}
      >
        Visit
      </span>
    </div>
  )
}
