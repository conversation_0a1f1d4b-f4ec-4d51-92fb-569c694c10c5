export function ProductsLoading() {
  return (
    <div className="space-y-8">
      {/* 过滤器骨架屏 */}
      <div className="w-full overflow-x-auto">
        <div className="flex items-center border-b min-w-max">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="h-10 bg-muted animate-pulse rounded-md w-24 mx-2"></div>
          ))}
        </div>
      </div>

      {/* 内容骨架屏 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div key={i} className="space-y-3">
            <div className="aspect-[16/9] bg-muted animate-pulse rounded-md"></div>
            <div className="h-5 bg-muted animate-pulse rounded w-2/3"></div>
            <div className="h-4 bg-muted animate-pulse rounded w-full"></div>
          </div>
        ))}
      </div>
    </div>
  )
}
