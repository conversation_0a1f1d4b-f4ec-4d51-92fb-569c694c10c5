import Link from "next/link"

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="border-t py-6 md:py-8">
      <div className="container flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-col gap-2">
          <Link href="/" className="font-semibold">
            Introducing.day
          </Link>
          <p className="text-sm text-muted-foreground">Discover the latest product launches</p>
        </div>
        <div className="flex flex-col md:flex-row gap-4 md:gap-6 text-sm">
          <Link href="/" className="hover:underline">
            Home
          </Link>
          <Link href="/collection" className="hover:underline">
            Collections
          </Link>
          <Link href="/blog" className="hover:underline">
            Blog
          </Link>
          <Link href="/story" className="hover:underline">
            Stories
          </Link>
          <Link href="/day1" className="hover:underline">
            Day 1 Club
          </Link>
          <Link href="/pricing" className="hover:underline">
            Pricing
          </Link>
        </div>
        <div className="text-sm text-muted-foreground">© {currentYear} Introducing.day. All rights reserved.</div>
      </div>
    </footer>
  )
}
