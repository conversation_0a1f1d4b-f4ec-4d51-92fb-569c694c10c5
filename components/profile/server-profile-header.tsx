import { User } from "@supabase/supabase-js"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card } from "@/components/ui/card"
import { SignOutButton } from "@/components/profile/sign-out-button"

interface ServerProfileHeaderProps {
  user: User | null
}

export function ServerProfileHeader({ user }: ServerProfileHeaderProps) {
  return (
    <Card className="p-6 mb-6">
      <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
        <Avatar className="w-24 h-24 border">
          <AvatarImage src={user?.user_metadata?.avatar_url} alt={user?.email || "User"} />
          <AvatarFallback className="text-2xl">
            {user?.email?.charAt(0).toUpperCase() || "U"}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 text-center md:text-left">
          <h1 className="text-2xl font-bold mb-1">
            {user?.user_metadata?.full_name || user?.email?.split("@")[0] || "User"}
          </h1>
          <p className="text-muted-foreground mb-4">{user?.email}</p>

          <div className="flex flex-wrap gap-3 justify-center md:justify-start">
            <SignOutButton />
          </div>
        </div>
      </div>
    </Card>
  )
}
