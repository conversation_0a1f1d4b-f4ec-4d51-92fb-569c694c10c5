import { User } from "@supabase/supabase-js"
import { Card } from "@/components/ui/card"
import { SmallCard } from "@/components/product/cards/small-card"
import { mapSupabaseProduct } from "@/lib/supabase/types"
import type { SupabaseProduct } from "@/lib/supabase/types"
import { EmptyState } from "@/components/profile/empty-state"
import { TabNavigation } from "@/components/profile/tab-navigation"

interface ServerProfileTabsProps {
  user: User | null
  upvotedProducts: SupabaseProduct[]
  activeTab: string
}

export function ServerProfileTabs({ user, upvotedProducts, activeTab }: ServerProfileTabsProps) {
  // Map Supabase products to the application's Product type
  const mappedProducts = upvotedProducts.map(mapSupabaseProduct)

  return (
    <div className="w-full">
      <TabNavigation activeTab={activeTab} productCount={mappedProducts.length} />

      <div className="mt-6">
        {activeTab === "upvoted" && (
          <div className="space-y-4">
            {mappedProducts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {mappedProducts.map((product) => (
                  <SmallCard key={product.id} product={product} />
                ))}
              </div>
            ) : (
              <EmptyState
                title={user ? "No upvoted products yet" : "Please log in to see your upvoted products"}
                description={user ? "Products you upvote will appear here." : "Log in to track your favorite products."}
                actionText={user ? "Explore Products" : "Sign In"}
                actionLink={user ? "/" : "/signin"}
              />
            )}
          </div>
        )}

        {activeTab === "submissions" && (
          <div className="space-y-4">
            <EmptyState
              title="No submissions yet"
              description="Products you submit will appear here."
              actionText="Submit a Product"
              actionLink="https://tally.so/r/wLaKdp"
              isExternalLink
            />
          </div>
        )}

        {activeTab === "settings" && (
          <div className="space-y-4">
            <Card className="p-6">
              <h3 className="text-lg font-medium mb-4">Account Details</h3>
              <p className="text-muted-foreground mb-4">
                View your account information and details.
              </p>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-1">Email</h4>
                  <p className="text-sm text-muted-foreground">{user?.email}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">Account Created</h4>
                  <p className="text-sm text-muted-foreground">
                    {user?.created_at ? new Date(user.created_at).toLocaleDateString() : "N/A"}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">Last Sign In</h4>
                  <p className="text-sm text-muted-foreground">
                    {user?.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : "N/A"}
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
