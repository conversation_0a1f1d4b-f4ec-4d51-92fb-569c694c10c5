import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"

interface EmptyStateProps {
  title: string
  description: string
  actionText?: string
  actionLink?: string
  isExternalLink?: boolean
}

export function EmptyState({
  title,
  description,
  actionText,
  actionLink,
  isExternalLink = false
}: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center text-center p-12 border border-dashed rounded-lg bg-gray-50">
      <h3 className="text-lg font-medium mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md">{description}</p>

      {actionText && actionLink && (
        isExternalLink ? (
          <Button asChild variant="outline">
            <a
              href={actionLink}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2"
            >
              {actionText}
              <ExternalLink size={16} />
            </a>
          </Button>
        ) : (
          <Button asChild variant="outline">
            <Link href={actionLink}>
              {actionText}
            </Link>
          </Button>
        )
      )}
    </div>
  )
}
