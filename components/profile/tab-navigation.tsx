"use client"

import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { cn } from "@/lib/utils"

interface TabNavigationProps {
  activeTab: string
  productCount: number
}

export function TabNavigation({ activeTab, productCount }: TabNavigationProps) {
  const searchParams = useSearchParams()

  const tabs = [
    {
      id: "upvoted",
      label: "Upvoted Products",
      href: "/profile?tab=upvoted",
      count: productCount
    },
    {
      id: "submissions", 
      label: "Your Submissions",
      href: "/profile?tab=submissions"
    },
    {
      id: "settings",
      label: "Account Details", 
      href: "/profile?tab=settings"
    }
  ]

  return (
    <div className="border-b border-gray-200">
      <nav className="-mb-px flex space-x-8" aria-label="Tabs">
        {tabs.map((tab) => (
          <Link
            key={tab.id}
            href={tab.href}
            className={cn(
              "whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm relative",
              activeTab === tab.id
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            )}
          >
            {tab.label}
            {tab.count !== undefined && tab.count > 0 && (
              <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-medium rounded-full bg-gray-100">
                {tab.count}
              </span>
            )}
          </Link>
        ))}
      </nav>
    </div>
  )
}
