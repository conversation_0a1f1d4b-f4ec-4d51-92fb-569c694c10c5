"use client"

import { useState } from "react"
import { Eye, Send, CheckCircle, AlertCircle, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface ProductDetails {
  name: string
  tagline: string
  description: string
  logoUrl: string
  coverImageUrl: string
  category: string
  tags: string[]
}

interface StepReviewProps {
  url: string
  details: ProductDetails
  selectedDate: Date | null
  onSubmit: () => Promise<void>
  onBack: () => void
}

export function StepReview({ url, details, selectedDate, onSubmit, onBack }: StepReviewProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      await onSubmit()
      setSubmitted(true)
    } catch (error) {
      console.error('Submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitted) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
            <h3 className="text-2xl font-bold text-green-700">Submission Successful!</h3>
            <p className="text-gray-600 max-w-md mx-auto">
              Thank you for submitting your product. We'll review it and get back to you within 2-3 business days.
            </p>
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">What happens next?</h4>
              <ul className="text-sm text-green-700 space-y-1 text-left">
                <li>• Our team will review your submission</li>
                <li>• You'll receive an email confirmation</li>
                <li>• If approved, your product will be scheduled for launch</li>
                <li>• You'll get a notification when it goes live</li>
              </ul>
            </div>
            <Button asChild className="mt-4">
              <a href="/">Return to Homepage</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <Eye className="w-5 h-5" />
          Review & Submit
        </CardTitle>
        <CardDescription>
          Please review all the information before submitting your product for approval.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Product Preview */}
        <div className="space-y-4">
          <h4 className="font-medium">Product Preview</h4>
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-start gap-4">
              {details.logoUrl && (
                <div className="w-16 h-16 bg-white rounded-lg border flex items-center justify-center overflow-hidden">
                  <img
                    src={details.logoUrl}
                    alt={details.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <h3 className="font-bold text-lg">{details.name}</h3>
                <p className="text-gray-600 mb-2">{details.tagline}</p>
                <p className="text-sm text-gray-500 line-clamp-3">{details.description}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="secondary">{details.category}</Badge>
                  {details.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {details.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{details.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Submission Details */}
        <div className="space-y-4">
          <h4 className="font-medium">Submission Details</h4>
          <div className="grid gap-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-500">Product URL:</span>
              <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline flex items-center gap-1"
              >
                {url.replace(/^https?:\/\//, '')}
                <ExternalLink className="w-3 h-3" />
              </a>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Category:</span>
              <span>{details.category}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Tags:</span>
              <span>{details.tags.length > 0 ? details.tags.join(', ') : 'None'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Preferred Launch Date:</span>
              <span>
                {selectedDate?.toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </span>
            </div>
          </div>
        </div>

        <Separator />

        {/* Important Notes */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Before you submit:</strong>
            <ul className="mt-2 space-y-1 text-sm">
              <li>• Make sure your product URL is working and accessible</li>
              <li>• Verify all information is accurate and up-to-date</li>
              <li>• Your submission will be reviewed within 2-3 business days</li>
              <li>• You'll receive email updates about your submission status</li>
            </ul>
          </AlertDescription>
        </Alert>

        {/* Action buttons */}
        <div className="flex gap-3 pt-4">
          <Button variant="outline" onClick={onBack} className="flex-1" disabled={isSubmitting}>
            Back
          </Button>
          <Button
            onClick={handleSubmit}
            className="flex-1"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Send className="w-4 h-4 mr-2 animate-pulse" />
                Submitting...
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                Submit Product
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
