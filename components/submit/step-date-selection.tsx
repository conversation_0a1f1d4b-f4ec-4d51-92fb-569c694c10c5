"use client"

import { useState } from "react"
import { Calendar, Clock, Star, Zap } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"

interface StepDateSelectionProps {
  selectedDate: Date | null
  onDateChange: (date: Date | null) => void
  onNext: () => void
  onBack: () => void
}

export function StepDateSelection({ selectedDate, onDateChange, onNext, onBack }: StepDateSelectionProps) {
  const [error, setError] = useState("")

  // Generate next 30 days for suggestions
  const generateDateSuggestions = () => {
    const suggestions = []
    const today = new Date()

    for (let i = 1; i <= 30; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)

      // Skip weekends for business products
      if (date.getDay() !== 0 && date.getDay() !== 6) {
        suggestions.push({
          date,
          label: formatDateLabel(date),
          availability: getDateAvailability(date),
          recommended: i <= 7 // First week is recommended
        })
      }
    }

    return suggestions.slice(0, 10) // Show top 10 suggestions
  }

  const formatDateLabel = (date: Date) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0) // Reset time to start of day for accurate comparison

    const targetDate = new Date(date)
    targetDate.setHours(0, 0, 0, 0)

    const diffTime = targetDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    const dayName = date.toLocaleDateString('en-US', { weekday: 'long' })
    const monthDay = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })

    if (diffDays === 1) return `Tomorrow, ${monthDay}`
    if (diffDays <= 7) return `${dayName}, ${monthDay}`
    return `${dayName}, ${monthDay}`
  }

  const getDateAvailability = (date: Date) => {
    // Simulate availability - in real app, this would come from API
    const dayOfWeek = date.getDay()
    const random = Math.random()

    if (dayOfWeek === 2 || dayOfWeek === 3) { // Tuesday, Wednesday
      return random > 0.3 ? 'high' : 'medium'
    } else if (dayOfWeek === 1 || dayOfWeek === 4) { // Monday, Thursday
      return random > 0.5 ? 'medium' : 'low'
    } else { // Friday
      return random > 0.7 ? 'low' : 'full'
    }
  }

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'high': return 'text-green-600 bg-green-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'low': return 'text-orange-600 bg-orange-50'
      case 'full': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getAvailabilityText = (availability: string) => {
    switch (availability) {
      case 'high': return 'Great availability'
      case 'medium': return 'Good availability'
      case 'low': return 'Limited spots'
      case 'full': return 'Fully booked'
      default: return 'Unknown'
    }
  }

  const handleDateSelect = (date: Date) => {
    setError("")
    onDateChange(date)
  }

  const handleContinue = () => {
    if (!selectedDate) {
      setError("Please select a launch date")
      return
    }

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const selected = new Date(selectedDate)
    selected.setHours(0, 0, 0, 0)

    if (selected <= today) {
      setError("Please select a future date")
      return
    }

    onNext()
  }

  const dateSuggestions = generateDateSuggestions()

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <Calendar className="w-5 h-5" />
          Choose Launch Date
        </CardTitle>
        <CardDescription>
          Select when you'd like your product to be featured. Earlier dates get more visibility!
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Quick date suggestions */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <Zap className="w-4 h-4" />
            Recommended Dates
          </h4>
          <div className="grid gap-2">
            {dateSuggestions.slice(0, 5).map((suggestion) => (
              <button
                key={suggestion.date.toISOString()}
                onClick={() => handleDateSelect(suggestion.date)}
                className={cn(
                  "flex items-center justify-between p-3 rounded-lg border text-left transition-colors hover:bg-gray-50",
                  selectedDate?.toDateString() === suggestion.date.toDateString()
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200"
                )}
              >
                <div className="flex items-center gap-3">
                  <div>
                    <div className="font-medium">{suggestion.label}</div>
                    <div className="text-sm text-gray-500">
                      {suggestion.date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </div>
                  </div>
                  {suggestion.recommended && (
                    <Badge variant="secondary" className="text-xs">
                      <Star className="w-3 h-3 mr-1" />
                      Recommended
                    </Badge>
                  )}
                </div>
                <Badge
                  variant="outline"
                  className={cn("text-xs", getAvailabilityColor(suggestion.availability))}
                >
                  {getAvailabilityText(suggestion.availability)}
                </Badge>
              </button>
            ))}
          </div>
        </div>

        {/* Calendar picker */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Or pick a custom date
          </h4>
          <div className="flex justify-center">
            <CalendarComponent
              mode="single"
              selected={selectedDate || undefined}
              onSelect={(date) => date && handleDateSelect(date)}
              disabled={(date) => date <= new Date()}
              className="rounded-md border"
            />
          </div>
        </div>

        {selectedDate && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Selected Launch Date</h4>
            <p className="text-blue-700">
              {selectedDate.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
            <p className="text-sm text-blue-600 mt-1">
              Your product will be reviewed and scheduled for this date.
            </p>
          </div>
        )}

        {error && (
          <p className="text-sm text-red-500 text-center">{error}</p>
        )}

        {/* Action buttons */}
        <div className="flex gap-3 pt-4">
          <Button variant="outline" onClick={onBack} className="flex-1">
            Back
          </Button>
          <Button onClick={handleContinue} className="flex-1">
            Continue
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
