"use client"

import { useState } from "react"
import { Edit3, Upload, Tag } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface ProductDetails {
  name: string
  tagline: string
  description: string
  logoUrl: string
  coverImageUrl: string
  category: string
  tags: string[]
}

interface StepProductDetailsProps {
  details: ProductDetails
  onDetailsChange: (details: ProductDetails) => void
  onNext: () => void
  onBack: () => void
}

const CATEGORIES = [
  "AI", "Developer Tools", "Design", "Productivity", "Marketing", 
  "Analytics", "E-commerce", "Education", "Finance", "Health", 
  "Social", "Entertainment", "Travel", "Other"
]

const SUGGESTED_TAGS = [
  "SaaS", "Mobile App", "Web App", "API", "Open Source", "Free", 
  "Subscription", "B2B", "B2C", "Startup", "Enterprise", "Chrome Extension",
  "iOS", "Android", "Mac", "Windows", "Cross-platform"
]

export function StepProductDetails({ details, onDetailsChange, onNext, onBack }: StepProductDetailsProps) {
  const [tagInput, setTagInput] = useState("")
  const [errors, setErrors] = useState<Record<string, string>>({})

  const updateDetail = (key: keyof ProductDetails, value: string | string[]) => {
    onDetailsChange({ ...details, [key]: value })
    // Clear error when user starts typing
    if (errors[key]) {
      setErrors({ ...errors, [key]: "" })
    }
  }

  const addTag = (tag: string) => {
    if (tag && !details.tags.includes(tag)) {
      updateDetail("tags", [...details.tags, tag])
    }
    setTagInput("")
  }

  const removeTag = (tagToRemove: string) => {
    updateDetail("tags", details.tags.filter(tag => tag !== tagToRemove))
  }

  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault()
      addTag(tagInput.trim())
    }
  }

  const validateAndContinue = () => {
    const newErrors: Record<string, string> = {}

    if (!details.name.trim()) newErrors.name = "Product name is required"
    if (!details.tagline.trim()) newErrors.tagline = "Tagline is required"
    if (!details.description.trim()) newErrors.description = "Description is required"
    if (!details.category) newErrors.category = "Category is required"

    if (details.name.length < 2) newErrors.name = "Product name must be at least 2 characters"
    if (details.tagline.length < 5) newErrors.tagline = "Tagline must be at least 5 characters"
    if (details.description.length < 10) newErrors.description = "Description must be at least 10 characters"

    setErrors(newErrors)

    if (Object.keys(newErrors).length === 0) {
      onNext()
    }
  }

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <Edit3 className="w-5 h-5" />
          Product Details
        </CardTitle>
        <CardDescription>
          Tell us about your product. You can edit the auto-generated information or start fresh.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Product Name */}
        <div className="space-y-2">
          <Label htmlFor="name">Product Name *</Label>
          <Input
            id="name"
            value={details.name}
            onChange={(e) => updateDetail("name", e.target.value)}
            placeholder="Enter your product name"
            className={errors.name ? "border-red-500" : ""}
          />
          {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
        </div>

        {/* Tagline */}
        <div className="space-y-2">
          <Label htmlFor="tagline">Tagline *</Label>
          <Input
            id="tagline"
            value={details.tagline}
            onChange={(e) => updateDetail("tagline", e.target.value)}
            placeholder="A short, catchy description of your product"
            className={errors.tagline ? "border-red-500" : ""}
          />
          {errors.tagline && <p className="text-sm text-red-500">{errors.tagline}</p>}
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description">Description *</Label>
          <Textarea
            id="description"
            value={details.description}
            onChange={(e) => updateDetail("description", e.target.value)}
            placeholder="Describe what your product does and what makes it special"
            rows={4}
            className={errors.description ? "border-red-500" : ""}
          />
          {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
        </div>

        {/* Logo URL */}
        <div className="space-y-2">
          <Label htmlFor="logoUrl">Logo URL (Optional)</Label>
          <Input
            id="logoUrl"
            type="url"
            value={details.logoUrl}
            onChange={(e) => updateDetail("logoUrl", e.target.value)}
            placeholder="https://example.com/logo.png"
          />
          <p className="text-xs text-gray-500">Direct link to your product logo (PNG, JPG, or SVG)</p>
        </div>

        {/* Cover Image URL */}
        <div className="space-y-2">
          <Label htmlFor="coverImageUrl">Cover Image URL (Optional)</Label>
          <Input
            id="coverImageUrl"
            type="url"
            value={details.coverImageUrl}
            onChange={(e) => updateDetail("coverImageUrl", e.target.value)}
            placeholder="https://example.com/cover.png"
          />
          <p className="text-xs text-gray-500">Direct link to a cover image or screenshot</p>
        </div>

        {/* Category */}
        <div className="space-y-2">
          <Label>Category *</Label>
          <Select value={details.category} onValueChange={(value) => updateDetail("category", value)}>
            <SelectTrigger className={errors.category ? "border-red-500" : ""}>
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              {CATEGORIES.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <Label>Tags (Optional)</Label>
          <div className="space-y-2">
            <Input
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyPress={handleTagInputKeyPress}
              placeholder="Add a tag and press Enter"
            />
            
            {/* Current tags */}
            {details.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {details.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                    {tag} ×
                  </Badge>
                ))}
              </div>
            )}

            {/* Suggested tags */}
            <div className="space-y-2">
              <p className="text-xs text-gray-500">Suggested tags:</p>
              <div className="flex flex-wrap gap-2">
                {SUGGESTED_TAGS.filter(tag => !details.tags.includes(tag)).slice(0, 8).map((tag) => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => addTag(tag)}
                  >
                    + {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-3 pt-4">
          <Button variant="outline" onClick={onBack} className="flex-1">
            Back
          </Button>
          <Button onClick={validateAndContinue} className="flex-1">
            Continue
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
