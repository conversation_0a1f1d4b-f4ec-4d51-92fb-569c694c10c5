"use client"

import { useState } from "react"
import { useAuth } from "@/components/auth/auth-provider"
import { ProgressIndicator } from "./progress-indicator"
import { StepUrlInput } from "./step-url-input"
import { StepProductDetails } from "./step-product-details"
import { StepDateSelection } from "./step-date-selection"
import { StepReview } from "./step-review"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { LogIn, UserPlus } from "lucide-react"
import Link from "next/link"
import { type ParsedProductInfo } from "@/lib/submit/url-parser"

interface ProductDetails {
  name: string
  tagline: string
  description: string
  logoUrl: string
  coverImageUrl: string
  category: string
  tags: string[]
}

const STEPS = [
  {
    title: "URL",
    description: "Enter product URL"
  },
  {
    title: "Details",
    description: "Edit product info"
  },
  {
    title: "Date",
    description: "Choose launch date"
  },
  {
    title: "Review",
    description: "Confirm & submit"
  }
]

export function MultiStepForm() {
  const { user, loading } = useAuth()
  const [currentStep, setCurrentStep] = useState(1)
  const [url, setUrl] = useState("")
  const [details, setDetails] = useState<ProductDetails>({
    name: "",
    tagline: "",
    description: "",
    logoUrl: "",
    coverImageUrl: "",
    category: "",
    tags: []
  })
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)

  // Show loading state while checking authentication
  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show login prompt if user is not authenticated
  if (!user) {
    return (
      <Card>
        <CardHeader className="text-center">
          <CardTitle>Sign In Required</CardTitle>
          <CardDescription>
            You need to be signed in to submit a product. Create an account or sign in to continue.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Why do I need to sign in?</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Track your submission status</li>
              <li>• Receive updates about your product</li>
              <li>• Manage your submitted products</li>
              <li>• Get notified when your product goes live</li>
            </ul>
          </div>

          <div className="flex gap-3">
            <Button asChild className="flex-1">
              <Link href={`/signin?redirectTo=${encodeURIComponent('/submit')}`}>
                <LogIn className="w-4 h-4 mr-2" />
                Sign In
              </Link>
            </Button>
            <Button asChild variant="outline" className="flex-1">
              <Link href={`/signup?redirectTo=${encodeURIComponent('/submit')}`}>
                <UserPlus className="w-4 h-4 mr-2" />
                Create Account
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const handleUrlChange = (newUrl: string) => {
    setUrl(newUrl)
  }

  const handleProductInfoParsed = (info: ParsedProductInfo) => {
    setDetails(prev => ({
      ...prev,
      name: info.name || prev.name,
      tagline: info.tagline || prev.tagline,
      description: info.description || prev.description,
      logoUrl: info.logoUrl || prev.logoUrl,
      coverImageUrl: info.coverImageUrl || prev.coverImageUrl,
    }))
  }

  const handleDetailsChange = (newDetails: ProductDetails) => {
    setDetails(newDetails)
  }

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date)
  }

  const handleSubmit = async () => {
    try {
      const response = await fetch('/api/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url,
          name: details.name,
          tagline: details.tagline,
          description: details.description,
          logoUrl: details.logoUrl,
          coverImageUrl: details.coverImageUrl,
          category: details.category,
          tags: details.tags,
          preferredDate: selectedDate?.toISOString(),
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Submission failed')
      }

      const result = await response.json()
      console.log('Submission successful:', result)
    } catch (error) {
      console.error('Submission error:', error)
      throw error
    }
  }

  const nextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      <ProgressIndicator
        currentStep={currentStep}
        totalSteps={STEPS.length}
        steps={STEPS}
      />

      <div className="mt-8">
        {currentStep === 1 && (
          <StepUrlInput
            url={url}
            onUrlChange={handleUrlChange}
            onProductInfoParsed={handleProductInfoParsed}
            onNext={nextStep}
          />
        )}

        {currentStep === 2 && (
          <StepProductDetails
            details={details}
            onDetailsChange={handleDetailsChange}
            onNext={nextStep}
            onBack={prevStep}
          />
        )}

        {currentStep === 3 && (
          <StepDateSelection
            selectedDate={selectedDate}
            onDateChange={handleDateChange}
            onNext={nextStep}
            onBack={prevStep}
          />
        )}

        {currentStep === 4 && (
          <StepReview
            url={url}
            details={details}
            selectedDate={selectedDate}
            onSubmit={handleSubmit}
            onBack={prevStep}
          />
        )}
      </div>
    </div>
  )
}
