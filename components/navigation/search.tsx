"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { SearchIcon, X, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { searchProductsClient } from "@/lib/supabase/db"
import type { Product } from "@/lib/supabase/types"
import { useOnClickOutside } from "@/hooks/use-click-outside"

interface SearchProps {
  onSearch?: (query: string) => void
  onExpandChange?: (expanded: boolean) => void
  className?: string
}

export function Search({ onSearch, onExpandChange, className }: SearchProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasSearched, setHasSearched] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Notify parent component about expansion state changes
  useEffect(() => {
    if (onExpandChange) {
      onExpandChange(isOpen)
    }
  }, [isOpen, onExpandChange])

  // Close search when clicking outside
  useOnClickOutside(searchRef, () => {
    setIsOpen(false)
    setQuery("")
    setResults([])
    setHasSearched(false)
  })

  // Handle search
  useEffect(() => {
    const handleSearch = async () => {
      if (!query.trim()) {
        setResults([])
        setHasSearched(false)
        return
      }

      setIsLoading(true)
      setHasSearched(true)

      try {
        const searchResults = await searchProductsClient(query)
        setResults(searchResults)
      } catch (error) {
        console.error("Error searching products:", error)
        setResults([])
      } finally {
        setIsLoading(false)
      }
    }

    // Debounce search
    const timeoutId = setTimeout(() => {
      handleSearch()
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query])

  // Handle opening the search
  const handleOpenSearch = () => {
    setIsOpen(true)
    // Focus the input after a short delay to ensure it's rendered
    setTimeout(() => {
      inputRef.current?.focus()
    }, 10)
  }

  // Handle closing the search
  const handleCloseSearch = () => {
    setIsOpen(false)
    setQuery("")
    setResults([])
    setHasSearched(false)
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (onSearch) {
      onSearch(query)
    }

    // If we have results and are on the homepage, keep the dropdown open
    // Otherwise navigate to homepage with search query
    if (results.length === 0 || window.location.pathname !== "/") {
      router.push(`/?search=${encodeURIComponent(query)}`)
      handleCloseSearch()
    }
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {isOpen ? (
        <div className="relative">
          <form onSubmit={handleSubmit} className="relative">
            <Input
              ref={inputRef}
              type="search"
              placeholder="Search products..."
              className="w-[200px] lg:w-[300px] pr-10 rounded-full h-10"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              autoComplete="off"
            />
            <div className="absolute right-0 top-0 h-full flex items-center pr-3">
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
              ) : (
                <SearchIcon className="h-4 w-4 text-muted-foreground" />
              )}
            </div>
          </form>

          {/* Search results dropdown */}
          {isOpen && query.trim() && (
            <div className="absolute top-full mt-1 w-full bg-white rounded-lg border shadow-lg overflow-hidden z-50">
              {isLoading ? (
                <div className="p-4 text-center">
                  <Loader2 className="h-5 w-5 animate-spin mx-auto text-muted-foreground" />
                  <p className="text-sm text-muted-foreground mt-2">Searching...</p>
                </div>
              ) : results.length > 0 ? (
                <div className="max-h-[400px] overflow-y-auto">
                  {results.map((product) => (
                    <Link
                      key={product.id}
                      href={`/product/${product.slug}`}
                      className="flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors"
                      onClick={handleCloseSearch}
                    >
                      <div className="relative h-10 w-10 bg-gray-100 rounded-md overflow-hidden flex-shrink-0">
                        <Image
                          src={product.logoUrl || "/placeholder.svg"}
                          alt={`${product.name} logo`}
                          fill
                          className="object-contain p-1"
                          sizes="40px"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm">{product.name}</h3>
                        <p className="text-xs text-muted-foreground truncate">{product.tagline}</p>
                      </div>
                    </Link>
                  ))}
                </div>
              ) : hasSearched ? (
                <div className="p-4 text-center">
                  <p className="text-sm text-muted-foreground">No products found for "{query}"</p>
                  <Button
                    variant="link"
                    size="sm"
                    className="mt-1 h-auto p-0"
                    onClick={() => {
                      if (onSearch) onSearch(query)
                      router.push(`/?search=${encodeURIComponent(query)}`)
                      handleCloseSearch()
                    }}
                  >
                    View all results
                  </Button>
                </div>
              ) : null}
            </div>
          )}
        </div>
      ) : (
        <Button
          onClick={handleOpenSearch}
          variant="outline"
          className="rounded-full w-10 h-10 flex items-center justify-center p-0 border-gray-200"
        >
          <SearchIcon className="h-4 w-4" />
          <span className="sr-only">Search</span>
        </Button>
      )}
    </div>
  )
}
