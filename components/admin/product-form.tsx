"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "sonner"

// 定义表单验证模式
const formSchema = z.object({
  name: z.string().min(2, "名称至少需要2个字符"),
  tagline: z.string().min(5, "标语至少需要5个字符"),
  description: z.string().min(10, "描述至少需要10个字符"),
  url: z.string().min(3, "URL至少需要3个字符"),
  logo_url: z.string().optional(),
  cover_image_url: z.string().optional(),
  category: z.string({
    required_error: "请选择一个分类",
  }),
  tags: z.string().optional(), // 表单中是字符串，提交时会转换为数组
  status: z.string({
    required_error: "请选择一个状态",
  }),
})

// 表单值类型
type ProductFormValues = z.infer<typeof formSchema>

// 数据库提交类型
interface ProductSubmitData {
  name: string;
  tagline: string;
  description: string;
  url: string;
  logo_url?: string;
  cover_image_url?: string;
  category: string;
  tags: string[] | string; // 可以是字符串数组或字符串
  status: string;
  created_at?: string;
  updated_at: string;
  published_at?: string;
}

interface ProductFormProps {
  product?: any
}

export default function ProductForm({ product }: ProductFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()
  const supabase = createClientComponentClient()



  // 初始化表单，使用产品数据或默认值
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: product?.name || "",
      tagline: product?.tagline || "",
      description: product?.description || "",
      url: product?.url || "",
      logo_url: product?.logo_url || "",
      cover_image_url: product?.cover_image_url || "",
      category: product?.category || "AI",
      tags: Array.isArray(product?.tags) ? product?.tags.join(", ") : (product?.tags || ""),
      status: product?.status || "draft",
    },
  })

  // 处理表单提交
  const onSubmit = async (values: ProductFormValues) => {
    setIsSubmitting(true)

    try {
      // Prepare base data, explicitly handling potentially null/undefined values
      const baseData = {
        name: values.name,
        tagline: values.tagline,
        description: values.description || null,
        url: values.url || null,
        category: values.category,
        tags: values.tags ? values.tags.split(",").map(tag => tag.trim()) : [],
        updated_at: new Date().toISOString(),
        // Logo and cover image URLs will be mapped later based on the target table
      };

      console.log("Submitting form values:", values);
      console.log("Prepared base data:", baseData);

      // If editing an existing product (always assumed to be in 'products' table for now)
      if (product?.id) {
        // For simplicity, updates target the 'products' table.
        // Status changes should ideally happen via review/schedule flows.
        const updateData: any = { ...baseData, status: values.status };
        updateData.logo_url = values.logo_url || null;       // Use logo_url for products table
        updateData.cover_image_url = values.cover_image_url || null; // Use cover_image_url for products table

        if (values.status === "published" && !product?.published_at) {
          updateData.published_at = new Date().toISOString();
        }

        console.log("Updating product with ID:", product.id, "Data:", updateData);
        const { error } = await supabase
          .from("products")
          .update(updateData)
          .eq("id", product.id);

        if (error) {
          console.error("Update error:", error);
          throw error;
        }

        toast.success("产品更新成功");
        router.refresh(); // Refresh after update too
      }
      // If creating a new product
      else {
        const insertData: any = {
          ...baseData,
          created_at: new Date().toISOString()
        };
        let targetTable: string;

        // Determine the target table and map fields accordingly
        if (values.status === 'published') {
          targetTable = 'products';
          insertData.published_at = new Date().toISOString();
          insertData.status = 'published';
          insertData.logo_url = values.logo_url || null; // products uses logo_url
          insertData.cover_image_url = values.cover_image_url || null; // products uses cover_image_url
        } else { // 'draft' or 'pending' status goes to reviews
          targetTable = 'product_reviews';
          // Map to product_reviews fields (assuming 'logo' and 'cover_image')
          insertData.logo = values.logo_url || null;
          insertData.cover_image = values.cover_image_url || null;
          // product_reviews might also need a status field
          insertData.status = values.status;
          // Remove the original url fields if they are not in the reviews table
          // delete insertData.logo_url;
          // delete insertData.cover_image_url;
        }

        const { error } = await supabase
          .from(targetTable)
          .insert([insertData]);

        if (error) {
          throw error;
        }

        toast.success("产品创建成功");
        router.push("/admin/products"); // Redirect after successful creation
        router.refresh(); // Refresh list page
      }

    } catch (error: any) {
      toast.error(error.message || "保存产品失败");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>名称</FormLabel>
                <FormControl>
                  <Input placeholder="产品名称" {...field} />
                </FormControl>
                <FormDescription>
                  您的产品名称
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>网址</FormLabel>
                <FormControl>
                  <Input placeholder="example.com" {...field} />
                </FormControl>
                <FormDescription>
                  产品网站地址（无需 http:// 或 https://）
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="tagline"
          render={({ field }) => (
            <FormItem>
              <FormLabel>标语</FormLabel>
              <FormControl>
                <Input placeholder="简短的产品描述" {...field} />
              </FormControl>
              <FormDescription>
                简短的产品描述（最多60个字符）
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>描述</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="详细描述您的产品"
                  className="min-h-32"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                产品的详细描述
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="logo_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Logo URL</FormLabel>
                <FormControl>
                  <Input placeholder="https://example.com/logo.png" {...field} />
                </FormControl>
                <FormDescription>
                  产品Logo的URL地址
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="cover_image_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>封面图片URL</FormLabel>
                <FormControl>
                  <Input placeholder="https://example.com/cover.png" {...field} />
                </FormControl>
                <FormDescription>
                  产品封面图片的URL地址
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>分类</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="选择一个分类" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="AI">AI</SelectItem>
                    <SelectItem value="Dev">开发工具</SelectItem>
                    <SelectItem value="Design">设计</SelectItem>
                    <SelectItem value="Productivity">生产力</SelectItem>
                    <SelectItem value="Marketing">营销</SelectItem>
                    <SelectItem value="Finance">金融</SelectItem>
                    <SelectItem value="Others">其他</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  产品所属的分类
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tags"
            render={({ field }) => (
              <FormItem>
                <FormLabel>标签</FormLabel>
                <FormControl>
                  <Input placeholder="AI, 工具, 生产力" {...field} />
                </FormControl>
                <FormDescription>
                  用逗号分隔的标签列表
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>状态</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择一个状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="draft">草稿</SelectItem>
                  <SelectItem value="pending">待审核</SelectItem>
                  <SelectItem value="published">已发布</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                产品的当前状态
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "保存中..." : (product ? "更新产品" : "创建产品")}
        </Button>
      </form>
    </Form>
  )
}
