-- Create voting tables for product voting functionality

-- Create product_votes table to store vote counts for each product
CREATE TABLE IF NOT EXISTS product_votes (
  id SERIAL PRIMARY KEY,
  product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  upvotes INTEGER NOT NULL DEFAULT 0,
  downvotes INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(product_id)
);

-- Create user_votes table to track individual user votes
CREATE TABLE IF NOT EXISTS user_votes (
  id SERIAL PRIMARY KEY,
  product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  vote_type VARCHAR(10) NOT NULL DEFAULT 'upvote' CHECK (vote_type IN ('upvote', 'downvote')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(product_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_product_votes_product_id ON product_votes(product_id);
CREATE INDEX IF NOT EXISTS idx_user_votes_product_id ON user_votes(product_id);
CREATE INDEX IF NOT EXISTS idx_user_votes_user_id ON user_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_user_votes_product_user ON user_votes(product_id, user_id);

-- Enable Row Level Security
ALTER TABLE product_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_votes ENABLE ROW LEVEL SECURITY;

-- RLS Policies for product_votes table
-- Anyone can read vote counts
CREATE POLICY "Anyone can read product votes" ON product_votes
  FOR SELECT
  TO public
  USING (true);

-- Only authenticated users can insert/update vote counts (this will be handled by triggers)
CREATE POLICY "Authenticated users can manage product votes" ON product_votes
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- RLS Policies for user_votes table
-- Users can only see their own votes
CREATE POLICY "Users can see their own votes" ON user_votes
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can only insert their own votes
CREATE POLICY "Users can insert their own votes" ON user_votes
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can only delete their own votes
CREATE POLICY "Users can delete their own votes" ON user_votes
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can only update their own votes
CREATE POLICY "Users can update their own votes" ON user_votes
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create function to update vote counts
CREATE OR REPLACE FUNCTION update_product_vote_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Insert or update product_votes record
    INSERT INTO product_votes (product_id, upvotes, downvotes)
    VALUES (NEW.product_id, 
            CASE WHEN NEW.vote_type = 'upvote' THEN 1 ELSE 0 END,
            CASE WHEN NEW.vote_type = 'downvote' THEN 1 ELSE 0 END)
    ON CONFLICT (product_id) 
    DO UPDATE SET 
      upvotes = product_votes.upvotes + CASE WHEN NEW.vote_type = 'upvote' THEN 1 ELSE 0 END,
      downvotes = product_votes.downvotes + CASE WHEN NEW.vote_type = 'downvote' THEN 1 ELSE 0 END,
      updated_at = now();
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Decrease vote count
    UPDATE product_votes 
    SET upvotes = GREATEST(0, upvotes - CASE WHEN OLD.vote_type = 'upvote' THEN 1 ELSE 0 END),
        downvotes = GREATEST(0, downvotes - CASE WHEN OLD.vote_type = 'downvote' THEN 1 ELSE 0 END),
        updated_at = now()
    WHERE product_id = OLD.product_id;
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Handle vote type change
    UPDATE product_votes 
    SET upvotes = upvotes 
                  - CASE WHEN OLD.vote_type = 'upvote' THEN 1 ELSE 0 END
                  + CASE WHEN NEW.vote_type = 'upvote' THEN 1 ELSE 0 END,
        downvotes = downvotes 
                    - CASE WHEN OLD.vote_type = 'downvote' THEN 1 ELSE 0 END
                    + CASE WHEN NEW.vote_type = 'downvote' THEN 1 ELSE 0 END,
        updated_at = now()
    WHERE product_id = NEW.product_id;
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update vote counts
CREATE TRIGGER trigger_update_product_vote_count
  AFTER INSERT OR UPDATE OR DELETE ON user_votes
  FOR EACH ROW
  EXECUTE FUNCTION update_product_vote_count();

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updating timestamps
CREATE TRIGGER trigger_product_votes_updated_at
  BEFORE UPDATE ON product_votes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_user_votes_updated_at
  BEFORE UPDATE ON user_votes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
