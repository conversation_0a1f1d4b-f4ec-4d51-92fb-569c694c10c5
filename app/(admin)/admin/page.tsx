import { createSupabaseServerClient } from "@/lib/supabase/server"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ChartAreaInteractive } from "@/components/charts/chart-area-interactive"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { PlusCircleIcon, ClockIcon, PackageIcon, CheckCircleIcon, CalendarIcon } from "lucide-react"
import ScheduledPublisher from "@/components/admin/scheduled-publisher"

interface ProductStatus {
  status: string;
}

export default async function AdminDashboardPage() {
  const supabase = await createSupabaseServerClient()

  // Get product stats
  const { data: products } = await supabase
    .from("products")
    .select("*")

  const totalProducts = products?.length || 0
  const pendingProducts = products?.filter((p: any) => p.status === "pending").length || 0
  const scheduledProducts = products?.filter((p: any) => p.status === "scheduled").length || 0
  const publishedProducts = products?.filter((p: any) => p.status === "published").length || 0

  // Get recent products
  const { data: recentProducts } = await supabase
    .from("products")
    .select("*")
    .order("created_at", { ascending: false })
    .limit(5)

  return (
    <div className="px-4 md:px-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-muted-foreground">
            Manage your products and monitor their status
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Link href="/admin/products/new">
            <Button>
              <PlusCircleIcon className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </Link>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Products
                </CardTitle>
                <PackageIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalProducts}</div>
                <p className="text-xs text-muted-foreground">
                  All products in the database
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Pending Review
                </CardTitle>
                <ClockIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{pendingProducts}</div>
                <p className="text-xs text-muted-foreground">
                  Products awaiting review
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Scheduled
                </CardTitle>
                <ClockIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{scheduledProducts}</div>
                <p className="text-xs text-muted-foreground">
                  Products scheduled for publication
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Published
                </CardTitle>
                <CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{publishedProducts}</div>
                <p className="text-xs text-muted-foreground">
                  Products live on the site
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Product Activity</CardTitle>
              </CardHeader>
              <CardContent className="pl-2">
                <ChartAreaInteractive />
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Recent Products</CardTitle>
                <CardDescription>
                  Recently added or updated products
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  {recentProducts?.map((product) => (
                    <div className="flex items-center" key={product.id}>
                      <div className="space-y-1">
                        <p className="text-sm font-medium leading-none">{product.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {product.tagline}
                        </p>
                      </div>
                      <div className="ml-auto font-medium">
                        <Link href={`/admin/products/${product.id}`}>
                          <Button variant="ghost" size="sm">View</Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <ScheduledPublisher />
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Product Analytics</CardTitle>
              <CardDescription>
                Detailed analytics will be available in a future update
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <p className="text-muted-foreground">Analytics dashboard coming soon</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
