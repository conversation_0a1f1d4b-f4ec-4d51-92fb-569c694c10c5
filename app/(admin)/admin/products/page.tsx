"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { format } from "date-fns"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { PlusIcon, PackageIcon, ClockIcon } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import ScheduleDateDialog from "@/components/admin/schedule-date-dialog"

// Define the structure for selected IDs state
interface SelectedIds {
  review: number[];
  scheduled: number[];
  published: number[];
  all: number[];
}

export default function ProductsPage() {
  // State for products from different tables
  const [publishedProducts, setPublishedProducts] = useState<any[]>([])
  const [reviewProducts, setReviewProducts] = useState<any[]>([])
  const [scheduledProducts, setScheduledProducts] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient()

  // State for selected product IDs
  const [selectedIds, setSelectedIds] = useState<SelectedIds>({
    review: [],
    scheduled: [],
    published: [],
    all: []
  });

  // State for batch action loading
  const [batchActionLoading, setBatchActionLoading] = useState(false);

  // State for date picker dialog
  const [isDateDialogOpen, setIsDateDialogOpen] = useState(false);

  // Initialize toast
  const { toast } = useToast();

  // Function to fetch all product data
  async function fetchAllProducts() {
    setLoading(true);
    setError(null);
    try {
      const [publishedRes, reviewRes, scheduledRes] = await Promise.all([
        supabase.from("products").select("*", { count: "exact" }).order("created_at", { ascending: false }),
        supabase.from("product_reviews").select("*", { count: "exact" }).order("created_at", { ascending: false }),
        supabase.from("product_schedules").select("*", { count: "exact" }).order("scheduled_at", { ascending: true })
      ]);

      if (publishedRes.error) throw publishedRes.error;
      if (reviewRes.error) throw reviewRes.error;
      if (scheduledRes.error) throw scheduledRes.error;

      setPublishedProducts(publishedRes.data || []);
      setReviewProducts(reviewRes.data || []);
      setScheduledProducts(scheduledRes.data || []);

      console.log("Fetched Published:", publishedRes.data);
      console.log("Fetched Reviews:", reviewRes.data);
      console.log("Fetched Scheduled:", scheduledRes.data);

    } catch (error: any) {
      console.error("Error fetching products:", error);
      setError("Failed to load products: " + error.message);
      setPublishedProducts([]);
      setReviewProducts([]);
      setScheduledProducts([]);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchAllProducts(); // Call the function inside useEffect
    // Set up listener for realtime changes (optional, can be added later)
    /*
    const channel = supabase
      .channel('realtime products')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'products' }, payload => {
        console.log('Change received!', payload)
        fetchAllProducts(); // Refetch on change
      })
      .on('postgres_changes', { event: '*', schema: 'public', table: 'product_reviews' }, payload => {
        console.log('Review Change received!', payload)
        fetchAllProducts(); // Refetch on change
      })
      .on('postgres_changes', { event: '*', schema: 'public', table: 'product_schedules' }, payload => {
        console.log('Schedule Change received!', payload)
        fetchAllProducts(); // Refetch on change
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
    */
  }, [supabase])

  // Combined list for the 'All' tab
  const allProducts = [
    ...publishedProducts.map(p => ({ ...p, status: "published" })),
    ...reviewProducts.map(p => ({ ...p, status: "review" })),
    ...scheduledProducts.map(p => ({ ...p, status: "scheduled" }))
  ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

  // Helper function to handle row selection toggle
  const handleSelectRow = (id: number, status: string) => {
    console.log(`Toggling row: ID=${id}, Status=${status}`);

    // Create a copy of the current state to modify
    const newSelectedIds = { ...selectedIds };

    // Get the current selection for this status
    const currentSelection = newSelectedIds[status as keyof SelectedIds] || [];

    // Toggle the selection
    if (currentSelection.includes(id)) {
      // Remove the ID if already selected
      newSelectedIds[status as keyof SelectedIds] = currentSelection.filter(selectedId => selectedId !== id);
    } else {
      // Add the ID if not already selected
      newSelectedIds[status as keyof SelectedIds] = [...currentSelection, id];
    }

    console.log('New selectedIds state:', newSelectedIds);

    // Update the state
    setSelectedIds(newSelectedIds);
  };

  // Helper function to handle select/deselect all
  const handleSelectAll = (status: keyof SelectedIds, productList: any[]) => {
    console.log(`Toggling all for status: ${status}`);

    // Create a copy of the current state to modify
    const newSelectedIds = { ...selectedIds };

    // Get all IDs from the product list
    const allIds = productList.map(p => p.id);

    // Check if all products are currently selected
    const allSelected = allIds.length > 0 &&
                       allIds.every(id => selectedIds[status].includes(id));

    // Toggle selection
    if (allSelected) {
      // If all are selected, clear the selection
      newSelectedIds[status] = [];
    } else {
      // Otherwise, select all
      newSelectedIds[status] = [...allIds];
    }

    console.log('New selectedIds state (select all):', newSelectedIds);

    // Update the state
    setSelectedIds(newSelectedIds);
  };

  // Helper function to handle select/deselect all for the 'All' tab
  const handleSelectAllCombined = () => {
    console.log("Toggling all products across all tabs");

    // Check if all products across all tabs are selected
    const allPublishedSelected = publishedProducts.every(p => selectedIds.published.includes(p.id));
    const allReviewsSelected = reviewProducts.every(p => selectedIds.review.includes(p.id));
    const allScheduledSelected = scheduledProducts.every(p => selectedIds.scheduled.includes(p.id));
    const allSelected = allPublishedSelected && allReviewsSelected && allScheduledSelected;

    // Create a copy of the current state
    const newSelectedIds = { ...selectedIds };

    if (allSelected) {
      // If all are selected, clear all selections
      newSelectedIds.published = [];
      newSelectedIds.review = [];
      newSelectedIds.scheduled = [];
    } else {
      // Otherwise, select all products across all tabs
      newSelectedIds.published = publishedProducts.map(p => p.id);
      newSelectedIds.review = reviewProducts.map(p => p.id);
      newSelectedIds.scheduled = scheduledProducts.map(p => p.id);
    }

    console.log('New combined selectedIds state:', newSelectedIds);
    setSelectedIds(newSelectedIds);
  };

  // Batch Action Handlers
  const handleBatchApprove = async (customScheduleDate?: Date) => {
    if (selectedIds.review.length === 0) return;
    setBatchActionLoading(true);

    const idsToApprove = selectedIds.review; // Store IDs
    console.log("Approving IDs:", idsToApprove);

    toast({
      title: "Batch Approve",
      description: `Attempting to approve ${idsToApprove.length} product(s)...`,
    });

    try {
      // 1. Get current user ID
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        throw new Error(userError?.message || "User not authenticated.");
      }

      // 2. Fetch the full product review data for the selected IDs
      const { data: reviewsToSchedule, error: fetchError } = await supabase
        .from("product_reviews")
        .select("*")
        .in("id", idsToApprove);

      if (fetchError) {
        console.error("Error fetching reviews for approval:", fetchError);
        throw fetchError;
      }

      if (!reviewsToSchedule || reviewsToSchedule.length === 0) {
        throw new Error("Could not find the selected products for approval.");
      }

      // 3. Calculate schedule date
      const scheduleDate = customScheduleDate || new Date();
      if (!customScheduleDate) {
        scheduleDate.setDate(scheduleDate.getDate() + 1); // Default schedule: 24 hours from now
      }
      const scheduledAtISO = scheduleDate.toISOString();
      const nowISO = new Date().toISOString();

      // 4. Insert each review one by one to avoid field mismatch issues
      let successCount = 0;
      let failedIds: number[] = [];

      for (const review of reviewsToSchedule) {
        try {
          // Create a minimal object with only essential fields
          const scheduleData: Record<string, any> = {
            name: review.name || "Unnamed Product",
            scheduled_at: scheduledAtISO,
            approved_by: user.id,
            review_id: review.id,
            created_at: nowISO,
            updated_at: nowISO
          };

          // Add optional fields if they exist in the review
          if (review.tagline !== undefined) scheduleData['tagline'] = review.tagline || "";
          if (review.description !== undefined) scheduleData['description'] = review.description;
          if (review.url !== undefined) scheduleData['url'] = review.url;
          if (review.category !== undefined) scheduleData['category'] = review.category || "other";
          if (review.tags !== undefined) scheduleData['tags'] = Array.isArray(review.tags) ? review.tags : [];

          // Handle image fields - try both naming conventions
          if (review.logo !== undefined) scheduleData['logo_url'] = review.logo;
          if (review.logo_url !== undefined) scheduleData['logo_url'] = review.logo_url;

          if (review.cover_image !== undefined) scheduleData['cover_image_url'] = review.cover_image;
          if (review.cover_image_url !== undefined) scheduleData['cover_image_url'] = review.cover_image_url;

          console.log("Inserting schedule data:", scheduleData);

          // Insert into product_schedules
          const { error: insertError } = await supabase
            .from("product_schedules")
            .insert([scheduleData]);

          if (insertError) {
            console.error(`Error inserting review ID ${review.id}:`, insertError);
            failedIds.push(review.id);
          } else {
            successCount++;

            // 注意：由于外键约束，我们不应该删除product_reviews中的记录
            // 因为product_schedules表中的review_id字段引用了它
            // 在实际应用中，可能需要添加一个状态字段来标记已处理的记录
            // 或者修改数据库结构，使用ON DELETE CASCADE外键约束

            // 暂时注释掉删除操作，避免外键约束错误
            /*
            const { error: deleteError } = await supabase
              .from("product_reviews")
              .delete()
              .eq("id", review.id);

            if (deleteError) {
              console.error(`Error deleting review ID ${review.id} after scheduling:`, deleteError);
            }
            */
          }
        } catch (itemError) {
          console.error(`Error processing review ID ${review.id}:`, itemError);
          failedIds.push(review.id);
        }
      }

      // 5. Show results toast
      if (successCount === reviewsToSchedule.length) {
        toast({
          title: "Batch Approve Successful",
          description: `All ${successCount} product(s) approved and scheduled successfully.`,
        });
      } else if (successCount > 0) {
        toast({
          title: "Partial Batch Approve",
          description: `${successCount} of ${reviewsToSchedule.length} product(s) scheduled. ${failedIds.length} failed.`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Batch Approve Failed",
          description: `Failed to schedule any products. Please check console for details.`,
          variant: "destructive",
        });
      }

      // 6. Refetch data & clear selection
      await fetchAllProducts();
      setSelectedIds(prev => ({ ...prev, review: [] }));

    } catch (error: any) {
      console.error("Failed during batch approve:", error);
      toast({
        title: "Batch Approve Failed",
        description: error.message || "An unknown error occurred while approving products.",
        variant: "destructive",
      });
    } finally {
      setBatchActionLoading(false);
    }
  };

  const handleBatchReject = async () => {
    if (selectedIds.review.length === 0) return;
    setBatchActionLoading(true);

    const idsToReject = selectedIds.review; // Store ids before potential state changes
    console.log("Rejecting IDs:", idsToReject);

    toast({
      title: "Batch Reject",
      description: `Attempting to reject ${idsToReject.length} product(s)...`,
    });

    try {
      // Log the IDs we're about to delete
      console.log("About to delete product_reviews with IDs:", idsToReject);

      const { error } = await supabase
        .from("product_reviews")
        .delete()
        .in("id", idsToReject);

      if (error) {
        console.error("Batch reject error:", error);
        throw error; // Throw error to be caught below
      }

      toast({
        title: "Batch Reject Successful",
        description: `${idsToReject.length} product(s) rejected successfully.`,
      });

      // Refetch data to update the list
      await fetchAllProducts();

      // Clear selection for the review tab
      setSelectedIds(prev => ({ ...prev, review: [] }));

    } catch (error: any) {
      console.error("Failed during batch reject:", error);
      toast({
        title: "Batch Reject Failed",
        description: error.message || "An unknown error occurred while rejecting products.",
        variant: "destructive",
      });
    } finally {
      setBatchActionLoading(false);
    }
  };

  const handleBatchPublish = async () => {
    if (selectedIds.scheduled.length === 0) return;
    setBatchActionLoading(true);

    const idsToPublish = selectedIds.scheduled;
    console.log("Publishing IDs:", idsToPublish);

    toast({
      title: "Batch Publish",
      description: `Attempting to publish ${idsToPublish.length} scheduled product(s)...`,
    });

    try {
      // 1. Fetch the full product schedule data for the selected IDs
      const { data: schedulesToPublish, error: fetchError } = await supabase
        .from("product_schedules")
        .select("*")
        .in("id", idsToPublish);

      if (fetchError) {
        console.error("Error fetching schedules for publishing:", fetchError);
        throw fetchError;
      }

      if (!schedulesToPublish || schedulesToPublish.length === 0) {
        throw new Error("Could not find the selected products for publishing.");
      }

      // Debug: Log the structure of the first scheduled item
      console.log("Schedule structure example:", schedulesToPublish[0]);

      // 2. Prepare data for products table
      const publishedAtISO = new Date().toISOString();
      const productInserts = schedulesToPublish.map(schedule => {
        // Create a clean object with only the fields we know exist in products table
        return {
          // Required or highly likely fields
          name: schedule.name,
          tagline: schedule.tagline || "",
          description: schedule.description || null,
          url: schedule.url || null,
          logo_url: schedule.logo_url || null, // Map logo_url
          cover_image_url: schedule.cover_image_url || null, // Map cover_image_url
          category: schedule.category || "other",
          tags: Array.isArray(schedule.tags) ? schedule.tags : [], // Ensure tags is an array
          status: 'published',
          published_at: publishedAtISO,
          created_at: schedule.created_at || publishedAtISO, // Keep original created_at if available
          updated_at: publishedAtISO,
        };
      });

      // Debug: Log the first item we're trying to insert
      console.log("First product insert data:", productInserts[0]);

      // 3. Insert into products
      const { error: insertError } = await supabase
        .from("products")
        .insert(productInserts);

      if (insertError) {
        console.error("Error inserting into products:", insertError);
        throw insertError;
      }

      // 4. Delete from product_schedules
      const { error: deleteError } = await supabase
        .from("product_schedules")
        .delete()
        .in("id", idsToPublish);

      if (deleteError) {
        console.error("Error deleting from product_schedules after publishing:", deleteError);
        toast({
          title: "Publish Warning",
          description: `Products published, but failed to remove ${idsToPublish.length} items from the schedule queue. Please check manually. Error: ${deleteError.message}`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Batch Publish Successful",
          description: `${idsToPublish.length} product(s) published successfully.`,
        });
      }

      // 5. Refetch data & clear selection
      await fetchAllProducts();
      setSelectedIds(prev => ({ ...prev, scheduled: [] }));

    } catch (error: any) {
      console.error("Failed during batch publish:", error);
      toast({
        title: "Batch Publish Failed",
        description: error.message || "An unknown error occurred while publishing products.",
        variant: "destructive",
      });
    } finally {
      setBatchActionLoading(false);
    }
  };

  const handleBatchUnschedule = async () => {
    if (selectedIds.scheduled.length === 0) return;
    setBatchActionLoading(true);

    const idsToUnschedule = selectedIds.scheduled;
    console.log("Unscheduling IDs:", idsToUnschedule);

    toast({
      title: "Batch Unschedule",
      description: `Attempting to unschedule ${idsToUnschedule.length} product(s)...`,
    });

    try {
      // 1. Fetch the full product schedule data
      const { data: schedulesToUnschedule, error: fetchError } = await supabase
        .from("product_schedules")
        .select("*")
        .in("id", idsToUnschedule);

      if (fetchError) {
        console.error("Error fetching schedules for unscheduling:", fetchError);
        throw fetchError;
      }

      if (!schedulesToUnschedule || schedulesToUnschedule.length === 0) {
        throw new Error("Could not find the selected products for unscheduling.");
      }

      // Debug: Log the structure of the first scheduled item
      console.log("Schedule to unschedule example:", schedulesToUnschedule[0]);

      // 2. Prepare data for product_reviews table
      const nowISO = new Date().toISOString();
      const reviewInserts = schedulesToUnschedule.map(schedule => {
        // Create a clean object with only the fields we know exist in product_reviews
        return {
          name: schedule.name,
          tagline: schedule.tagline || "",
          description: schedule.description || null,
          url: schedule.url || null,
          logo_url: schedule.logo_url || null, // Map logo_url
          cover_image_url: schedule.cover_image_url || null, // Map cover_image_url
          category: schedule.category || "other",
          tags: Array.isArray(schedule.tags) ? schedule.tags : [], // Ensure tags is an array
          status: 'draft', // Set status back to draft
          created_at: schedule.created_at || nowISO, // Preserve original creation if possible
          updated_at: nowISO,
        };
      });

      // Debug: Log the first item we're trying to insert
      console.log("First review insert data:", reviewInserts[0]);

      // 3. Insert into product_reviews
      const { error: insertError } = await supabase
        .from("product_reviews")
        .insert(reviewInserts);

      if (insertError) {
        console.error("Error inserting into product_reviews:", insertError);
        throw insertError;
      }

      // 4. Delete from product_schedules
      const { error: deleteError } = await supabase
        .from("product_schedules")
        .delete()
        .in("id", idsToUnschedule);

      if (deleteError) {
        console.error("Error deleting from product_schedules after unscheduling:", deleteError);
        toast({
          title: "Unschedule Warning",
          description: `Products moved to reviews, but failed to remove ${idsToUnschedule.length} items from the schedule queue. Please check manually. Error: ${deleteError.message}`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Batch Unschedule Successful",
          description: `${idsToUnschedule.length} product(s) unscheduled and moved to reviews.`,
        });
      }

      // 5. Refetch data & clear selection
      await fetchAllProducts();
      setSelectedIds(prev => ({ ...prev, scheduled: [] }));

    } catch (error: any) {
      console.error("Failed during batch unschedule:", error);
      toast({
        title: "Batch Unschedule Failed",
        description: error.message || "An unknown error occurred while unscheduling products.",
        variant: "destructive",
      });
    } finally {
      setBatchActionLoading(false);
    }
  };

  const handleBatchUnpublish = async () => {
    if (selectedIds.published.length === 0) return;
    setBatchActionLoading(true);

    const idsToUnpublish = selectedIds.published;
    console.log("Unpublishing IDs:", idsToUnpublish);

    toast({
      title: "Batch Unpublish",
      description: `Attempting to unpublish ${idsToUnpublish.length} product(s)...`,
    });

    try {
      // 1. Fetch the full product data
      const { data: productsToUnpublish, error: fetchError } = await supabase
        .from("products")
        .select("*")
        .in("id", idsToUnpublish);

      if (fetchError) {
        console.error("Error fetching products for unpublishing:", fetchError);
        throw fetchError;
      }

      if (!productsToUnpublish || productsToUnpublish.length === 0) {
        throw new Error("Could not find the selected products for unpublishing.");
      }

      // Debug: Log the structure of the first product
      console.log("Product to unpublish example:", productsToUnpublish[0]);

      // 2. Prepare data for product_reviews table
      const nowISO = new Date().toISOString();
      const reviewInserts = productsToUnpublish.map(product => {
        // Create a clean object with only the fields we know exist in product_reviews
        return {
          name: product.name,
          tagline: product.tagline || "",
          description: product.description || null,
          url: product.url || null,
          logo_url: product.logo_url || null, // Map logo_url
          cover_image_url: product.cover_image_url || null, // Map cover_image_url
          category: product.category || "other",
          tags: Array.isArray(product.tags) ? product.tags : [], // Ensure tags is an array
          status: 'draft', // Set status back to draft
          created_at: product.created_at || nowISO, // Preserve original creation if possible
          updated_at: nowISO,
        };
      });

      // Debug: Log the first item we're trying to insert
      console.log("First review insert data (from unpublish):", reviewInserts[0]);

      // 3. Insert into product_reviews
      const { error: insertError } = await supabase
        .from("product_reviews")
        .insert(reviewInserts);

      if (insertError) {
        console.error("Error inserting into product_reviews during unpublish:", insertError);
        throw insertError;
      }

      // 4. Delete from products
      const { error: deleteError } = await supabase
        .from("products")
        .delete()
        .in("id", idsToUnpublish);

      if (deleteError) {
        console.error("Error deleting from products after unpublishing:", deleteError);
        toast({
          title: "Unpublish Warning",
          description: `Products moved to reviews, but failed to remove ${idsToUnpublish.length} items from the published list. Please check manually. Error: ${deleteError.message}`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Batch Unpublish Successful",
          description: `${idsToUnpublish.length} product(s) unpublished and moved to reviews.`,
        });
      }

      // 5. Refetch data & clear selection
      await fetchAllProducts();
      setSelectedIds(prev => ({ ...prev, published: [] }));

    } catch (error: any) {
      console.error("Failed during batch unpublish:", error);
      toast({
        title: "Batch Unpublish Failed",
        description: error.message || "An unknown error occurred while unpublishing products.",
        variant: "destructive",
      });
    } finally {
      setBatchActionLoading(false);
    }
  };

  // 添加批量设置发布时间的函数
  const handleSetBatchScheduleDate = () => {
    // 打开日期选择对话框
    setIsDateDialogOpen(true);
  };

  // 处理日期选择对话框确认
  const handleDateDialogConfirm = (scheduledDate: Date) => {
    // 调用批量审核函数并传入自定义日期
    handleBatchApprove(scheduledDate);
  };

  // Get badge for status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "review":
        return <Badge variant="secondary">Pending Review</Badge>
      case "scheduled":
        return <Badge variant="outline" className="bg-amber-100 text-amber-800 hover:bg-amber-100">Scheduled</Badge>
      case "published":
        return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">Published</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      {/* 日期选择对话框 */}
      <ScheduleDateDialog
        isOpen={isDateDialogOpen}
        onClose={() => setIsDateDialogOpen(false)}
        onConfirm={handleDateDialogConfirm}
      />

      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Products</h2>
        <div className="flex items-center gap-2">
          <Link href="/admin/products/new">
            <Button>
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </Link>
        </div>
      </div>



      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All ({allProducts.length})</TabsTrigger>
          <TabsTrigger value="review">Pending Review ({reviewProducts.length})</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled ({scheduledProducts.length})</TabsTrigger>
          <TabsTrigger value="published">Published ({publishedProducts.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Products</CardTitle>
              <CardDescription>
                Manage all products across all states
              </CardDescription>
            </CardHeader>
            <CardContent>
              {(selectedIds.review.length > 0 || selectedIds.scheduled.length > 0 || selectedIds.published.length > 0) && (
                <div className="flex flex-wrap items-center gap-2 mb-4 p-3 border border-gray-200 rounded-md bg-gray-50 shadow-sm">
                  <span className="text-sm font-medium text-gray-700">
                    {selectedIds.review.length + selectedIds.scheduled.length + selectedIds.published.length} selected:
                  </span>

                  {/* Review actions */}
                  {selectedIds.review.length > 0 && (
                    <>
                      <span className="text-xs bg-secondary text-secondary-foreground px-2 py-1 rounded-full">
                        {selectedIds.review.length} pending
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBatchApprove()}
                        disabled={batchActionLoading}
                        className="bg-white"
                      >
                        Approve Selected
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={handleBatchReject}
                        disabled={batchActionLoading}
                      >
                        Reject Selected
                      </Button>
                    </>
                  )}

                  {/* Scheduled actions */}
                  {selectedIds.scheduled.length > 0 && (
                    <>
                      <span className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded-full">
                        {selectedIds.scheduled.length} scheduled
                      </span>
                      <Button
                        variant="default"
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                        onClick={handleBatchPublish}
                        disabled={batchActionLoading}
                      >
                        Publish Selected Now
                      </Button>
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={handleBatchUnschedule}
                        disabled={batchActionLoading}
                        className="bg-white"
                      >
                        Unschedule Selected
                      </Button>
                    </>
                  )}

                  {/* Published actions */}
                  {selectedIds.published.length > 0 && (
                    <>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        {selectedIds.published.length} published
                      </span>
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={handleBatchUnpublish}
                        disabled={batchActionLoading}
                        className="bg-white"
                      >
                        Unpublish Selected
                      </Button>
                    </>
                  )}
                </div>
              )}
              {loading ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">Loading...</p>
                </div>
              ) : allProducts.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox
                          checked={
                            publishedProducts.length > 0 && reviewProducts.length > 0 && scheduledProducts.length > 0 &&
                            publishedProducts.every(p => selectedIds.published.includes(p.id)) &&
                            reviewProducts.every(p => selectedIds.review.includes(p.id)) &&
                            scheduledProducts.every(p => selectedIds.scheduled.includes(p.id))
                          }
                          onCheckedChange={handleSelectAllCombined}
                          aria-label="Select all rows across all tabs"
                        />
                      </TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>URL</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allProducts.map((product) => (
                      <TableRow key={`${product.status}-${product.id}`}>
                        <TableCell>
                          <Checkbox
                            checked={selectedIds[product.status as keyof SelectedIds]?.includes(product.id)}
                            onCheckedChange={() => handleSelectRow(product.id, product.status)}
                            aria-label={`Select row ${product.id}`}
                            data-product-id={product.id} // Add data attribute for debugging
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {product.logo_url || product.logo ? (
                              <img
                                src={product.logo_url || product.logo}
                                alt={product.name}
                                className="h-8 w-8 rounded-full object-cover"
                              />
                            ) : (
                              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                                <PackageIcon className="h-4 w-4" />
                              </div>
                            )}
                            <div>
                              <div>{product.name}</div>
                              <div className="text-sm text-muted-foreground">{product.tagline}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{product.url}</TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell>{getStatusBadge(product.status || '')}</TableCell>
                        <TableCell>{product.created_at ? format(new Date(product.created_at), "yyyy-MM-dd") : '-'}</TableCell>
                        <TableCell className="text-right">
                          {product.status === 'published' && (
                            <Link href={`/admin/products/${product.id}`}>
                              <Button variant="outline" size="sm">
                                View/Edit
                              </Button>
                            </Link>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">
                    {error ? `Error loading products: ${error}` : 'No products found'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="review" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Review</CardTitle>
              <CardDescription>
                Products waiting for review
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedIds.review.length > 0 && (
                <div className="flex items-center gap-2 mb-4 p-3 border border-gray-200 rounded-md bg-gray-50 shadow-sm">
                  <span className="text-sm font-medium text-gray-700">{selectedIds.review.length} selected:</span>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleSetBatchScheduleDate}
                    disabled={batchActionLoading}
                  >
                    批量设置发布时间 ({selectedIds.review.length})
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleBatchApprove()}
                    disabled={batchActionLoading}
                  >
                    批量通过 ({selectedIds.review.length})
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBatchReject}
                    disabled={batchActionLoading}
                  >
                    Reject Selected
                  </Button>
                </div>
              )}
              {loading ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">Loading...</p>
                </div>
              ) : reviewProducts.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox
                          checked={reviewProducts.length > 0 &&
                                  reviewProducts.every(p => selectedIds.review.includes(p.id))}
                          onCheckedChange={() => handleSelectAll('review', reviewProducts)}
                          aria-label="Select all review rows"
                        />
                      </TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>URL</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reviewProducts.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedIds.review.includes(product.id)}
                            onCheckedChange={() => handleSelectRow(product.id, 'review')}
                            aria-label={`Select review row ${product.id}`}
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {product.logo_url || product.logo ? (
                              <img
                                src={product.logo_url || product.logo}
                                alt={product.name}
                                className="h-8 w-8 rounded-full object-cover"
                              />
                            ) : (
                              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                                <PackageIcon className="h-4 w-4" />
                              </div>
                            )}
                            <div>
                              <div>{product.name}</div>
                              <div className="text-sm text-muted-foreground">{product.tagline}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{product.url}</TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell>{product.created_at ? format(new Date(product.created_at), "yyyy-MM-dd") : '-'}</TableCell>
                        <TableCell className="text-right">
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No products pending review</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Products</CardTitle>
              <CardDescription>
                Products scheduled for publication
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedIds.scheduled.length > 0 && (
                <div className="flex items-center gap-2 mb-4 p-3 border border-gray-200 rounded-md bg-gray-50 shadow-sm">
                  <span className="text-sm font-medium text-gray-700">{selectedIds.scheduled.length} selected:</span>
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-green-600 hover:bg-green-700"
                    onClick={handleBatchPublish}
                    disabled={batchActionLoading}
                  >
                    Publish Selected Now
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handleBatchUnschedule}
                    disabled={batchActionLoading}
                    className="bg-white"
                  >
                    Unschedule Selected
                  </Button>
                </div>
              )}
              {loading ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">Loading...</p>
                </div>
              ) : scheduledProducts.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox
                          checked={scheduledProducts.length > 0 &&
                                  scheduledProducts.every(p => selectedIds.scheduled.includes(p.id))}
                          onCheckedChange={() => handleSelectAll('scheduled', scheduledProducts)}
                          aria-label="Select all scheduled rows"
                        />
                      </TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>URL</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Scheduled For</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {scheduledProducts.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedIds.scheduled.includes(product.id)}
                            onCheckedChange={() => handleSelectRow(product.id, 'scheduled')}
                            aria-label={`Select scheduled row ${product.id}`}
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {product.logo_url || product.logo ? (
                              <img
                                src={product.logo_url || product.logo}
                                alt={product.name}
                                className="h-8 w-8 rounded-full object-cover"
                              />
                            ) : (
                              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                                <ClockIcon className="h-4 w-4" />
                              </div>
                            )}
                            <div>
                              <div>{product.name}</div>
                              <div className="text-sm text-muted-foreground">{product.tagline}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{product.url}</TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell>
                          <span className="font-medium text-amber-700">
                            {product.scheduled_at ? format(new Date(product.scheduled_at), "yyyy-MM-dd HH:mm") : '-'}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No scheduled products</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="published" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Published Products</CardTitle>
              <CardDescription>
                Products that are live on the site
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedIds.published.length > 0 && (
                <div className="flex items-center gap-2 mb-4 p-3 border border-gray-200 rounded-md bg-gray-50 shadow-sm">
                  <span className="text-sm font-medium text-gray-700">{selectedIds.published.length} selected:</span>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handleBatchUnpublish}
                    disabled={batchActionLoading}
                    className="bg-white"
                  >
                    Unpublish Selected
                  </Button>
                </div>
              )}
              {loading ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">Loading...</p>
                </div>
              ) : publishedProducts.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox
                          checked={publishedProducts.length > 0 &&
                                  publishedProducts.every(p => selectedIds.published.includes(p.id))}
                          onCheckedChange={() => handleSelectAll('published', publishedProducts)}
                          aria-label="Select all published rows"
                        />
                      </TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>URL</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Published</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {publishedProducts.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedIds.published.includes(product.id)}
                            onCheckedChange={() => handleSelectRow(product.id, 'published')}
                            aria-label={`Select published row ${product.id}`}
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {product.logo_url || product.logo ? (
                              <img
                                src={product.logo_url || product.logo}
                                alt={product.name}
                                className="h-8 w-8 rounded-full object-cover"
                              />
                            ) : (
                              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                                <PackageIcon className="h-4 w-4" />
                              </div>
                            )}
                            <div>
                              <div>{product.name}</div>
                              <div className="text-sm text-muted-foreground">{product.tagline}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{product.url}</TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell>{product.published_at ? format(new Date(product.published_at), "yyyy-MM-dd") : '-'}</TableCell>
                        <TableCell className="text-right">
                          <Link href={`/admin/products/${product.id}`}>
                            <Button variant="outline" size="sm">
                              View/Edit
                            </Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No published products</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
