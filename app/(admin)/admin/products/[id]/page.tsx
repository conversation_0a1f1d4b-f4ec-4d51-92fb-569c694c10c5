import Link from "next/link"
import { notFound } from "next/navigation"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { ArrowLeftIcon } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import ProductForm from "@/components/admin/product-form"
import DeleteProduct from "@/components/admin/delete-product"

interface ProductPageProps {
  params: {
    id: string
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  const supabase = await createSupabaseServerClient()

  // 获取产品数据
  const { data: product, error } = await supabase
    .from("products")
    .select("*")
    .eq("id", params.id)
    .single()

  if (error || !product) {
    notFound()
  }

  return (
    <div className="px-4 md:px-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2">
            <Link href="/admin/products">
              <Button variant="ghost" size="sm">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                返回产品列表
              </Button>
            </Link>
          </div>
          <h2 className="text-3xl font-bold tracking-tight mt-2">编辑产品</h2>
          <p className="text-muted-foreground">
            更新产品信息和设置
          </p>
        </div>
        <DeleteProduct productId={product.id} productName={product.name} />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>产品信息</CardTitle>
        </CardHeader>
        <CardContent>
          <ProductForm product={product} />
        </CardContent>
      </Card>
    </div>
  )
}
