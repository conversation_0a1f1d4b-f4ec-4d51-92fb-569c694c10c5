import type React from "react"
import type { Metada<PERSON> } from "next"
import { AuthProvider } from "@/components/auth/auth-provider"
import "./globals.css"

export const metadataBase = new URL('https://introducing.day')
export const metadata: Metadata = {
  title: "Introducing.day – Launch and Discover new Products",
  description: "Discover amazing products from top creators around the world every day.",
  openGraph: {
    title: "Introducing.day – Launch and Discover new Products",
    description: "Discover amazing products from top creators around the world every day.",
    url: "https://introducing.day",
    siteName: "Introducing.day",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Introducing.day",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Introducing.day – Launch and Discover new Products",
    description: "Discover amazing products from top creators around the world every day.",
    creator: "@introducingday",
    images: ["/og-image.png"],
  },
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-icon.png',
  },
  alternates: {
    canonical: "https://introducing.day",
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preload" href="/fonts/Outfit-Regular.ttf" as="font" type="font/ttf" crossOrigin="anonymous" />
        <link rel="preload" href="/fonts/7-segment-display.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      </head>
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
