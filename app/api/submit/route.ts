import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/server"

interface SubmissionData {
  url: string
  name: string
  tagline: string
  description: string
  logoUrl?: string
  coverImageUrl?: string
  category: string
  tags: string[]
  preferredDate?: string
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()

    // Check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const body: SubmissionData = await request.json()

    // Validate required fields
    if (!body.url || !body.name || !body.tagline || !body.description || !body.category) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Prepare data for product_reviews table
    // Note: product_reviews table uses 'logo' and 'cover_image' field names
    const reviewData = {
      name: body.name,
      tagline: body.tagline,
      description: body.description,
      url: body.url,
      logo: body.logoUrl || null,
      cover_image: body.coverImageUrl || null,
      category: body.category,
      tags: body.tags.length > 0 ? body.tags.join(', ') : null,
      submitted_by: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    // Insert into product_reviews table
    const { data, error } = await supabase
      .from("product_reviews")
      .insert([reviewData])
      .select()
      .single()

    if (error) {
      console.error("Error inserting product review:", error)
      return NextResponse.json({ error: "Failed to submit product" }, { status: 500 })
    }

    // If user specified a preferred date, we could store it separately
    // For now, we'll just include it in the response
    const response = {
      success: true,
      message: "Product submitted successfully",
      reviewId: data.id,
      preferredDate: body.preferredDate,
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error in submit API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
