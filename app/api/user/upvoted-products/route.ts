import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient, createSupabaseAdminClient } from "@/lib/supabase/server"
import { mapSupabaseProduct } from "@/lib/supabase/types"

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()

    // Get the current user (more secure than getSession)
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError) {
      console.error("User error:", userError)
      return NextResponse.json({ error: "Authentication error" }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    // Get user's upvoted products
    const { data: userVotes, error: votesError } = await supabase
      .from("user_votes")
      .select("product_id, vote_type, created_at")
      .eq("user_id", user.id)
      .eq("vote_type", "upvote")
      .order("created_at", { ascending: false })

    if (votesError) {
      console.error("Error fetching user votes:", votesError)
      return NextResponse.json({ error: "Failed to fetch votes" }, { status: 500 })
    }

    // Get product details
    const upvotedProductIds = userVotes?.map(vote => vote.product_id) || []

    if (upvotedProductIds.length === 0) {
      return NextResponse.json([])
    }

    // Use admin client for product data to ensure we can read all products
    const adminSupabase = createSupabaseAdminClient()
    const { data: products, error: productsError } = await adminSupabase
      .from("products")
      .select("*")
      .in("id", upvotedProductIds)
      .order("created_at", { ascending: false })

    if (productsError) {
      console.error("Error fetching products:", productsError)
      return NextResponse.json({ error: "Failed to fetch products" }, { status: 500 })
    }

    // Map to application format
    const mappedProducts = (products || []).map(mapSupabaseProduct)

    return NextResponse.json(mappedProducts)

  } catch (error) {
    console.error("Error in upvoted products API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
