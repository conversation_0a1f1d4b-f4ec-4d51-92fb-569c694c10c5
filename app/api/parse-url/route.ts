import { NextRequest, NextResponse } from "next/server"
import { simpleParser } from "@/lib/parsers"

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json()

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 })
    }

    // 验证 URL 格式
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`)
    } catch {
      return NextResponse.json({ error: "Invalid URL format" }, { status: 400 })
    }

    console.log("Parsing URL:", url)

    // 使用简化解析器解析 URL
    const result = await simpleParser.parse(url)

    console.log("Parse result:", result)

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error("Error in parse-url API:", error)
    return NextResponse.json({ 
      error: "Failed to parse URL",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
