import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { mapSupabaseProduct } from "@/lib/supabase/types"

export async function GET(request: NextRequest) {
  // 从查询参数中获取 IDs
  const { searchParams } = new URL(request.url)
  const idsParam = searchParams.get("ids")

  if (!idsParam) {
    return NextResponse.json({ error: "No IDs provided" }, { status: 400 })
  }

  // 解析 ID 参数
  const ids = idsParam.split(",").map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id))

  if (ids.length === 0) {
    return NextResponse.json({ error: "Invalid IDs provided" }, { status: 400 })
  }

  try {
    const supabase = await createSupabaseServerClient()

    const { data, error } = await supabase
      .from("products")
      .select("*")
      .in("id", ids)
      .order("created_at", { ascending: false })

    if (error) {
      console.error(`Error fetching products with IDs ${ids.join(", ")}:`, error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // 转换数据并返回
    const products = data.map(mapSupabaseProduct)
    return NextResponse.json(products)
  } catch (error) {
    console.error("Error in products by IDs API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
