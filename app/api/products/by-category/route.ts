import { NextRequest, NextResponse } from "next/server"
import { getProductsByCategory } from "@/lib/supabase/db-server"

export async function GET(request: NextRequest) {
  try {
    // 从 URL 参数中获取分类
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("category")

    if (!category) {
      return NextResponse.json({ error: "Category parameter is required" }, { status: 400 })
    }

    // 获取指定分类的产品
    const products = await getProductsByCategory(category)

    // 返回产品数据
    return NextResponse.json(products)
  } catch (error) {
    console.error("Error in products by category API:", error)
    return NextResponse.json({ error: "Failed to fetch products" }, { status: 500 })
  }
}
