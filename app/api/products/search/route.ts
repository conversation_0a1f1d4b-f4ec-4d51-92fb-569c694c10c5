import { NextRequest, NextResponse } from "next/server"
import { searchProducts } from "@/lib/supabase/db-server"

export async function GET(request: NextRequest) {
  try {
    // 从 URL 参数中获取搜索查询
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("query")

    if (!query) {
      return NextResponse.json({ error: "Query parameter is required" }, { status: 400 })
    }

    // 搜索产品
    const products = await searchProducts(query)

    // 返回产品数据
    return NextResponse.json(products)
  } catch (error) {
    console.error("Error in products search API:", error)
    return NextResponse.json({ error: "Failed to search products" }, { status: 500 })
  }
}
