import type { Metadata } from "next"
import CollectionCard from "@/components/product/cards/collection-card"
import { collectionsConfig } from "@/lib/config/site-config"
import { getProductsByIds } from "@/lib/supabase/db-server"

export const metadata: Metadata = {
  title: "Collections | Introducing.day",
  description: "Curated collections of products on Introducing.day",
}

// 处理集合数据，为每个集合添加封面图
export default async function CollectionPage() {
  // 从配置文件获取集合数据
  const collections = await Promise.all(
    collectionsConfig.map(async (collection) => {
      // 获取每个集合的前三个产品
      const products = await getProductsByIds(collection.productIds.slice(0, 3))

      // 提取产品的封面图
      const coverImages = products.map(product => product.coverImageUrl || "/covers/default-cover.png")

      return {
        ...collection,
        coverImages,
      }
    })
  )

  return (
    <>
      <section className="pt-16 pb-6 md:pt-28 md:pb-10">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center space-y-4">
            <h1 className="text-3xl md:text-5xl font-bold tracking-tighter font-outfit">Collections</h1>
            <p className="text-lg text-muted-foreground max-w-2xl">
              Discover curated collections of products grouped by category, use case, or theme.
            </p>
          </div>
        </div>
      </section>

      <div className="container py-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {collections.map((collection) => (
            <CollectionCard
              key={collection.slug}
              title={collection.title}
              subtitle={collection.subtitle}
              slug={collection.slug}
              coverImages={collection.coverImages}
            />
          ))}
        </div>
      </div>
    </>
  )
}
