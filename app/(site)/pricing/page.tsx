import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardT<PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, Star } from "lucide-react"
import { Accordion, Accordion<PERSON>ontent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Pricing | Introducing.day",
  description: "Choose your launch plan and get the visibility your project deserves with our flexible launch options.",
}

export default function PricingPage() {
  return (
    <div className="container mx-auto py-12 px-4 max-w-6xl">
      {/* Header Section */}
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl font-bold mb-6">Choose Your Launch Plan</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Get the visibility your project deserves with our flexible launch options. All launches happen at 8:00 AM UTC.
        </p>
      </div>

      {/* Pricing Cards */}
      <div className="grid md:grid-cols-3 gap-6 lg:gap-8 mb-20 max-w-5xl mx-auto">
        {/* Free Launch */}
        <Card className="relative hover:shadow-lg transition-shadow">
          <CardHeader className="pb-8">
            <CardTitle className="text-xl">Free Launch</CardTitle>
            <div className="flex items-baseline gap-1">
              <span className="text-4xl font-bold">$0</span>
              <span className="text-muted-foreground">/launch</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Standard launch with up to 90 days scheduling window.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-sm">5 slots available daily</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-sm">Standard launch queue</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-sm">Featured on homepage</span>
              </div>
              <div className="flex items-start gap-3">
                <Check className="h-4 w-4 text-green-600 mt-0.5" />
                <div className="text-sm">
                  <div>Dofollow Backlink only if:</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    1. Top 3 daily ranking<br />
                    2. Display our badge on your site
                  </div>
                </div>
              </div>
            </div>
            <Button asChild className="w-full mt-6" variant="outline">
              <Link href="https://tally.so/r/wLaKdp">Launch for Free</Link>
            </Button>
          </CardContent>
        </Card>

        {/* Premium Launch */}
        <Card className="relative border-blue-200 shadow-lg hover:shadow-xl transition-shadow">
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <Badge className="bg-blue-600 text-white px-4 py-1">Most Popular</Badge>
          </div>
          <CardHeader className="pb-8 pt-8">
            <CardTitle className="text-xl">Premium Launch</CardTitle>
            <div className="flex items-baseline gap-1">
              <span className="text-4xl font-bold">$10</span>
              <span className="text-muted-foreground">/launch</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Priority scheduling with faster launch dates.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Check className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Skip the Free Queue - Priority access</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Guaranteed Dofollow Backlink (DR 20)</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-4 w-4 text-blue-600" />
                <span className="text-sm">12 premium slots daily</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Earlier launch dates</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Featured on homepage</span>
              </div>
            </div>
            <Button asChild className="w-full mt-6 bg-blue-600 hover:bg-blue-700">
              <Link href="https://tally.so/r/wLaKdp?plan=premium">Get Premium</Link>
            </Button>
          </CardContent>
        </Card>

        {/* Premium Plus */}
        <Card className="relative border-orange-200 shadow-lg hover:shadow-xl transition-shadow">
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <Badge className="bg-orange-600 text-white px-4 py-1">Early Bird</Badge>
          </div>
          <CardHeader className="pb-8 pt-8">
            <CardTitle className="text-xl">Premium Plus</CardTitle>
            <div className="flex items-baseline gap-1">
              <span className="text-4xl font-bold">$12.5</span>
              <span className="text-muted-foreground text-sm line-through">$25</span>
            </div>
            <div className="text-xs text-green-600 font-medium">-50% for early users</div>
            <p className="text-sm text-muted-foreground">
              Ultimate visibility with special homepage spot.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm font-medium text-orange-600 mb-3">
              Everything in Premium, plus:
            </div>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Star className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium">Premium Spotlight Placement</span>
              </div>
              <div className="text-xs text-muted-foreground ml-7">
                Most visible position on our platform
              </div>
              <div className="flex items-center gap-3">
                <Star className="h-4 w-4 text-orange-600" />
                <span className="text-sm">Guaranteed Dofollow Backlink</span>
              </div>
              <div className="text-xs text-muted-foreground ml-7">
                Valuable SEO boost from our DR 20 domain
              </div>
              <div className="flex items-center gap-3">
                <Star className="h-4 w-4 text-orange-600" />
                <span className="text-sm">Exclusive Daily Spots</span>
              </div>
              <div className="text-xs text-muted-foreground ml-7">
                Only 3 projects per day
              </div>
              <div className="flex items-center gap-3">
                <Star className="h-4 w-4 text-orange-600" />
                <span className="text-sm">Fastest Launch Dates</span>
              </div>
              <div className="text-xs text-muted-foreground ml-7">
                Top priority for your project
              </div>
            </div>
            <Button asChild className="w-full mt-6 bg-orange-600 hover:bg-orange-700">
              <Link href="https://tally.so/r/wLaKdp?plan=premium-plus">Get Premium Plus</Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* FAQ Section */}
      <div className="max-w-3xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="item-1">
            <AccordionTrigger>When do launches happen?</AccordionTrigger>
            <AccordionContent>
              All launches happen at 8:00 AM UTC. We launch a limited number of projects each day to ensure quality visibility.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger>How many projects are launched each day?</AccordionTrigger>
            <AccordionContent>
              We maintain a careful balance to ensure each project gets proper attention. Free launches have 5 daily slots, Premium has 12 slots, and Premium Plus has only 3 exclusive spots per day.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger>How far in advance can I schedule my launch?</AccordionTrigger>
            <AccordionContent>
              Free launches can be scheduled up to 90 days in advance. Premium and Premium Plus users get priority scheduling with faster available dates and more flexible timing options.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger>What is a Dofollow Backlink and why is it valuable?</AccordionTrigger>
            <AccordionContent>
              A dofollow backlink from our DR 20 domain helps improve your website's SEO ranking. For free launches, you'll only receive this if you rank in the top 3 daily and display our badge. Premium users get guaranteed dofollow backlinks regardless of ranking.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-5">
            <AccordionTrigger>What makes Premium Spotlight Placement special?</AccordionTrigger>
            <AccordionContent>
              Premium Spotlight Placement gives your project the most visible position on our platform, ensuring maximum exposure to our audience. This premium positioning significantly increases your project's visibility and potential for engagement.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  )
}
