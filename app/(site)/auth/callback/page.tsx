"use client"

import { useEffect, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Inner component to handle the auth logic
function AuthHandler() {
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleAuthCallback = async () => {
      const supabase = createSupabaseClient()
      const code = searchParams.get("code")

      if (code) {
        try {
          await supabase.auth.exchangeCodeForSession(code)
          router.push("/profile")
        } catch (error) {
          router.push("/signin")
        }
      } else {
        router.push("/signin")
      }
    }

    handleAuthCallback()
  }, [router, searchParams])

  // This component doesn't render anything itself, the loading UI is the fallback
  return null;
}

export default function AuthCallbackPage() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Verifying your account</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <Suspense fallback={
            <>
              <div className="flex justify-center my-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
              </div>
              <p>Please wait while we verify your account...</p>
            </>
          }>
            <AuthHandler />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
