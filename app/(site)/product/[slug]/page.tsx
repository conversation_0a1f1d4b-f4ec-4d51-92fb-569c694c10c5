import React from "react"
import type { Metadata } from "next"
import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import {
  Calendar,
  Globe,
  Tag,
  FilterIcon,
  Bot,
  BarChart3,
  <PERSON><PERSON>,
  Wrench,
  TrendingUp,
  SproutIcon as Seedling,
  Lock,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { formatDate } from "@/lib/utils"
import { BigCard } from "@/components/product/cards/big-card"
import { getProductBySlug, getRelatedProducts } from "@/lib/supabase/db-server"

interface ProductPageProps {
  params: {
    slug: string
  }
}

// 为产品详情页生成静态元数据
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  // 在 Next.js 15 中，需要先 await params
  const { slug } = await params
  // 添加缓存策略，重用已获取的数据
  const product = await getProductBySlug(slug)

  if (!product) {
    return {
      title: "Product Not Found | Introducing.day",
      description: "The requested product could not be found.",
    }
  }

  const metadata: Metadata = {
    title: `${product.name} - ${product.tagline} | Introducing.day`,
    description: product.description || product.tagline,
    openGraph: {
      title: product.name,
      description: product.tagline,
      images: [
        {
          url: product.coverImageUrl,
          width: 1200,
          height: 630,
          alt: product.name,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: product.name,
      description: product.tagline,
      images: [product.coverImageUrl],
    },
  }
  return metadata
}

// 使用 React.cache 包装数据获取函数已经在 db.ts 中实现

export default async function ProductPage({ params }: ProductPageProps) {
  // 在 Next.js 15 中，需要先 await params
  const { slug } = await params

  // 使用缓存版本的数据获取函数
  const product = await getProductBySlug(slug)

  if (!product) {
    notFound()
  }

  // 使用混合推荐策略获取相关产品
  const relatedProducts = await getRelatedProducts(product, 6)

  // Map categories to icons
  const categoryIcons: Record<string, React.ReactNode> = {
    All: <FilterIcon size={18} />,
    AI: <Bot size={18} />,
    Productivity: <BarChart3 size={18} />,
    Tools: <Wrench size={18} />,
    Design: <Palette size={18} />,
    Marketing: <TrendingUp size={18} />,
    Development: <Seedling size={18} />,
  }

  // Get the icon for the current product's category
  const categoryIcon = categoryIcons[product.category] || <FilterIcon size={18} />

  return (
    <div className="container py-8 md:py-12">
      <div className="max-w-3xl mx-auto">
        {/* Product header */}
        <div className="flex items-center gap-4 mb-6">
          <div className="relative h-12 w-12 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
            <Image
              src={product.logoUrl || "/placeholder.svg"}
              alt={`${product.name} logo`}
              fill
              className="object-contain p-2"
              sizes="48px"
              priority={true} // 优先加载logo
              quality={90}
              placeholder="blur"
              blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEtAJJXIDTjwAAAABJRU5ErkJggg=="
            />
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold">{product.name}</h1>
            <p className="text-muted-foreground">{product.tagline}</p>
          </div>

          {product.url && (
            <Button size="sm" variant="outline" asChild className="gap-2 border-gray-200 hover:bg-gray-50">
              <a href={`https://${product.url}`} target="_blank" rel="noopener noreferrer">
                <Globe size={16} />
                <span>Visit Website</span>
              </a>
            </Button>
          )}
        </div>

        {/* Cover image */}
        <div className="relative aspect-[16/9] w-full rounded-lg overflow-hidden border mb-8 bg-gray-50">
          <Image
            src={product.coverImageUrl || "/placeholder.svg"}
            alt={product.name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, 768px"
            priority={true} // 优先加载封面图片
            quality={85}
            placeholder="blur"
            blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEtAJJXIDTjwAAAABJRU5ErkJggg=="
          />
        </div>

        {/* Tabs */}
        <Tabs defaultValue="overview" className="mb-6 w-full">
          <TabsList className="w-full grid grid-cols-3 bg-gray-100 rounded-lg mb-4">
            <TabsTrigger value="overview" className="data-[state=active]:bg-white rounded-lg">
              Overview
            </TabsTrigger>
            <TabsTrigger value="comments" className="data-[state=active]:bg-white rounded-lg">
              Comments
            </TabsTrigger>
            <TabsTrigger value="deals" className="data-[state=active]:bg-white rounded-lg">
              Deals
            </TabsTrigger>
          </TabsList>

          {/* Tab content with white background */}
          <div className="bg-white rounded-lg border">
            <TabsContent value="overview" className="p-6">
              {/* Description */}
              {product.description && (
                <div className="prose max-w-none">
                  <p className="text-base leading-relaxed">{product.description}</p>
                </div>
              )}

              {/* Add a placeholder space */}
              <div className="flex-grow"></div>

              {/* Product info bar */}
              <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground pt-4 pb-0">
                <div className="flex items-center gap-1.5">
                  {categoryIcon}
                  <span>{product.category}</span>
                </div>

                <span className="text-gray-300">•</span>

                <div className="flex items-center gap-1.5">
                  <Calendar size={18} />
                  <span>{formatDate(product.createdAt)}</span>
                </div>

                {product.tags && product.tags.length > 0 && (
                  <>
                    <span className="text-gray-300">•</span>
                    <div className="flex items-center gap-1.5">
                      <Tag size={18} />
                      <div className="flex flex-wrap gap-1">
                        {product.tags.map((tag, index) => (
                          <React.Fragment key={tag}>
                            <span>{tag}</span>
                            {index < product.tags.length - 1 && <span className="text-gray-300">,</span>}
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </TabsContent>

            <TabsContent value="comments" className="p-6">
              <div className="py-12 flex flex-col items-center justify-center text-center">
                <Lock className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-xl font-medium text-gray-600 mb-6">Please login to view comments</h3>
                <Button asChild className="bg-black hover:bg-black/90">
                  <Link href="/signin">Login</Link>
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="deals" className="p-6">
              <div className="py-12 flex flex-col items-center justify-center text-center">
                <Lock className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-xl font-medium text-gray-600 mb-6">Please login to view deals</h3>
                <Button asChild className="bg-black hover:bg-black/90">
                  <Link href="/signin">Login</Link>
                </Button>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* Related Products - centered title */}
      {relatedProducts.length > 0 && (
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-8 text-center">You might also like</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {relatedProducts.map((relatedProduct) => (
              <BigCard key={relatedProduct.id} product={relatedProduct} />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
