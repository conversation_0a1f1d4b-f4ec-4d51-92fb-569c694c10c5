import type { Metadata } from "next"
import { MultiStepForm } from "@/components/submit/multi-step-form"

export const metadata: Metadata = {
  title: "Submit Your Product | Introducing.day",
  description:
    "Submit your product to Introducing.day and get discovered by our community of early adopters and innovators.",
}

export default function SubmitPage() {
  return (
    <div className="container py-12 md:py-20">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-5xl font-bold tracking-tighter font-outfit mb-4">Submit Your Product</h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Get your product in front of thousands of early adopters, innovators, and potential customers.
          </p>
        </div>

        {/* Multi-step form */}
        <MultiStepForm />
      </div>
    </div>
  )
}
