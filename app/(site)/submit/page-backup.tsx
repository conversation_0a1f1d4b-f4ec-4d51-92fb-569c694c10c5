import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { Check } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export const metadata: Metadata = {
  title: "Submit Your Product | Introducing.day",
  description:
    "Submit your product to Introducing.day and get discovered by our community of early adopters and innovators.",
}

export default function SubmitPage() {
  return (
    <div className="container py-12 md:py-20">
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12 md:mb-16">
          <h1 className="text-3xl md:text-5xl font-bold tracking-tighter font-outfit mb-4">Submit Your Product</h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Get your product in front of thousands of early adopters, innovators, and potential customers.
          </p>
        </div>

        {/* Plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Free Plan */}
          <Card className="border-gray-200 flex flex-col">
            <CardHeader>
              <CardTitle className="text-xl">Free</CardTitle>
              <CardDescription>Get started with basic visibility</CardDescription>
              <div className="mt-4">
                <span className="text-3xl font-bold">$0</span>
                <span className="text-muted-foreground ml-1">/product</span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4 flex-grow">
              <p className="text-sm text-muted-foreground">
                Perfect for indie makers looking to get initial exposure without investment.
              </p>

              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Get discovered by our community of early adopters</span>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Receive valuable feedback from potential users</span>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Build credibility with a presence on Introducing.day</span>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Appear in category listings for targeted visibility</span>
                </div>
              </div>

              <div className="pt-4">
                <p className="text-sm font-medium mb-2">Visibility areas:</p>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Category pages</li>
                  <li>• Product detail page</li>
                  <li>• Search results</li>
                </ul>
              </div>

              <div className="pt-4">
                <Badge variant="outline" className="bg-gray-50">
                  Backlink required
                </Badge>
              </div>
            </CardContent>
            <CardFooter className="mt-auto pt-6">
              <Button asChild className="w-full" variant="outline">
                <Link href="https://tally.so/r/wLaKdp">Submit for Free</Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Day 1 Club Plan */}
          <Card className="border-gray-200 relative flex flex-col">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-teal-400 via-green-400 to-lime-400"></div>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl">Day 1 Club</CardTitle>
                <Badge className="bg-gradient-to-r from-teal-400 via-green-400 to-lime-400 text-white">Popular</Badge>
              </div>
              <CardDescription>Boost your launch momentum</CardDescription>
              <div className="mt-4">
                <span className="text-3xl font-bold">$49</span>
                <span className="text-muted-foreground ml-1">/product</span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4 flex-grow">
              <p className="text-sm text-muted-foreground">
                Ideal for startups looking to accelerate user acquisition and build early traction.
              </p>

              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Attract your first 100+ users from our engaged community</span>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Feature in the New Arrival section for maximum launch visibility</span>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Get recommended to users interested in your category</span>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Appear in the Timeline view for sustained discovery</span>
                </div>
              </div>

              <div className="pt-4">
                <p className="text-sm font-medium mb-2">Visibility areas:</p>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• New Arrival section (homepage)</li>
                  <li>• Timeline feature</li>
                  <li>• Category pages</li>
                  <li>• Recommendation areas</li>
                  <li>• Product detail page</li>
                  <li>• Search results</li>
                </ul>
              </div>

              <div className="pt-4">
                <Badge variant="outline" className="bg-gray-50">
                  Backlink required
                </Badge>
              </div>
            </CardContent>
            <CardFooter className="mt-auto pt-6">
              <Button asChild className="w-full">
                <Link href="https://tally.so/r/wLaKdp?plan=day1">Join Day 1 Club</Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Supporter Plan */}
          <Card className="border-gray-200 bg-gray-50 flex flex-col">
            <CardHeader>
              <CardTitle className="text-xl">Supporter</CardTitle>
              <CardDescription>Maximize your product's exposure</CardDescription>
              <div className="mt-4">
                <span className="text-3xl font-bold">$149</span>
                <span className="text-muted-foreground ml-1">/product</span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4 flex-grow">
              <p className="text-sm text-muted-foreground">
                Perfect for established products seeking premium placement and maximum conversion.
              </p>

              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Drive significant traffic with premium site-wide visibility</span>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Feature in the coveted Highlight section on the homepage</span>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>Get included in curated Collections for targeted exposure</span>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <span>No backlink requirement - maximum flexibility for your site</span>
                </div>
              </div>

              <div className="pt-4">
                <p className="text-sm font-medium mb-2">Visibility areas:</p>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Highlight section (homepage)</li>
                  <li>• New Arrival section</li>
                  <li>• Timeline feature</li>
                  <li>• Category pages</li>
                  <li>• Collections feature</li>
                  <li>• Site-wide promotion</li>
                  <li>• Enhanced product detail page</li>
                  <li>• Priority in search results</li>
                </ul>
              </div>

              <div className="pt-4">
                <Badge variant="outline" className="bg-white">
                  No backlink required
                </Badge>
              </div>
            </CardContent>
            <CardFooter className="mt-auto pt-6">
              <Button asChild className="w-full bg-black hover:bg-black/90 text-white">
                <Link href="https://tally.so/r/wLaKdp?plan=supporter">Become a Supporter</Link>
              </Button>
            </CardFooter>
          </Card>
        </div>

        {/* Results Section */}
        <div className="mt-16 max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold mb-6 text-center">Real Results for Makers</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="p-6">
              <div className="text-3xl font-bold mb-2">500+</div>
              <p className="text-muted-foreground">Average new users for Day 1 Club products in first week</p>
            </div>
            <div className="p-6">
              <div className="text-3xl font-bold mb-2">15,000+</div>
              <p className="text-muted-foreground">Monthly visitors discovering new products</p>
            </div>
            <div className="p-6">
              <div className="text-3xl font-bold mb-2">32%</div>
              <p className="text-muted-foreground">Average conversion rate from Supporter placements</p>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16 max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold mb-6 text-center">Frequently Asked Questions</h2>

          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">What is a backlink?</h3>
              <p className="text-muted-foreground">
                A backlink is a simple "Featured on Introducing.day" badge on your website that helps us grow our
                community. For Free and Day 1 Club plans, this helps us bring more potential users to discover your
                product.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">How long will my product be featured?</h3>
              <p className="text-muted-foreground">
                Your product remains on Introducing.day indefinitely. Premium placements (New Arrival, Highlight, etc.)
                typically last for 30 days, giving you maximum exposure during your launch period.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">When will my product appear after submission?</h3>
              <p className="text-muted-foreground">
                Free submissions are reviewed within 5-7 business days. Day 1 Club and Supporter submissions are
                prioritized and typically go live within 24-48 hours after approval.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Can I upgrade my plan later?</h3>
              <p className="text-muted-foreground">
                Absolutely! You can upgrade at any time to increase your product's visibility and reach more potential
                users. Contact us after submission if you'd like to upgrade.
              </p>
            </div>
          </div>
        </div>

        {/* Testimonials */}
        <div className="mt-16 border-t pt-16">
          <h2 className="text-2xl font-bold mb-8 text-center">What Makers Say</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg border">
              <p className="italic text-muted-foreground mb-4">
                "Being featured on Introducing.day brought us over 500 new users in the first week. The quality of
                traffic was amazing - these were users who actually engaged with our product."
              </p>
              <div className="flex items-center">
                <div className="font-medium">Alex Chen</div>
                <span className="mx-2">•</span>
                <div className="text-sm text-muted-foreground">Founder, TaskFlow</div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border">
              <p className="italic text-muted-foreground mb-4">
                "The Day 1 Club plan was perfect for our launch. We got featured in the right places and connected with
                early adopters who provided invaluable feedback that shaped our roadmap."
              </p>
              <div className="flex items-center">
                <div className="font-medium">Sarah Johnson</div>
                <span className="mx-2">•</span>
                <div className="text-sm text-muted-foreground">Co-founder, DesignBuddy</div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border">
              <p className="italic text-muted-foreground mb-4">
                "As a Supporter, we received incredible exposure across the platform. The ROI was 10x our investment -
                we gained not just users but enterprise clients who discovered us here."
              </p>
              <div className="flex items-center">
                <div className="font-medium">Michael Torres</div>
                <span className="mx-2">•</span>
                <div className="text-sm text-muted-foreground">CEO, DataViz Pro</div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to accelerate your product's growth?</h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Join hundreds of successful products that found their first users on Introducing.day
          </p>
          <Button asChild size="lg" className="bg-black hover:bg-black/90 text-white px-8">
            <Link href="https://tally.so/r/wLaKdp">Submit Your Product</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
