import { Metadata } from "next"
import { ServerProfileHeader } from "@/components/profile/server-profile-header"
import { ServerProfileTabs } from "@/components/profile/server-profile-tabs"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"

export const metadata: Metadata = {
  title: "Your Profile | Introducing.day",
  description: "View and manage your Introducing.day profile",
}

export default async function ProfilePage({
  searchParams,
}: {
  searchParams: Promise<{ tab?: string }>
}) {
  const supabase = await createSupabaseServerClient()

  // Get the current user (more secure than getSession)
  const { data: { user }, error: userError } = await supabase.auth.getUser()

  // 如果用户未登录，重定向到登录页面
  if (!user) {
    redirect('/signin')
  }

  // 获取用户点赞的产品
  let upvotedProducts = []

  const { data: userVotes, error: votesError } = await supabase
    .from("user_votes")
    .select("product_id, vote_type, created_at")
    .eq("user_id", user.id)
    .eq("vote_type", "upvote")
    .order("created_at", { ascending: false })

  if (votesError) {
    console.error("Error fetching user votes:", votesError)
  }

  // 获取用户点赞的产品详情
  const upvotedProductIds = userVotes?.map(vote => vote.product_id) || []

  if (upvotedProductIds.length > 0) {
    const { data: products, error: productsError } = await supabase
      .from("products")
      .select("*")
      .in("id", upvotedProductIds)
      .order("created_at", { ascending: false })

    if (productsError) {
      console.error("Error fetching products:", productsError)
    } else {
      upvotedProducts = products || []
    }
  }

  // 获取当前活动的标签页，默认为 "upvoted"
  const resolvedSearchParams = await searchParams
  const activeTab = resolvedSearchParams.tab || "upvoted"

  return (
    <div className="container py-8 max-w-5xl">
      <ServerProfileHeader user={user} />
      <ServerProfileTabs
        user={user}
        upvotedProducts={upvotedProducts}
        activeTab={activeTab}
      />
    </div>
  )
}
