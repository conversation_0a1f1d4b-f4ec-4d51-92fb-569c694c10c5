import { MetadataRoute } from 'next'
import { getProducts, getProductDates } from '@/lib/supabase/db-server'
import { collectionsConfig } from '@/lib/config/site-config'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // 基础路由
  const baseRoutes = [
    {
      url: 'https://introducing.day',
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: 'https://introducing.day/collection',
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: 'https://introducing.day/story',
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: 'https://introducing.day/blog',
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
  ] as MetadataRoute.Sitemap

  // 获取所有产品
  const products = await getProducts()
  const productRoutes = products.map((product) => ({
    url: `https://introducing.day/product/${product.slug}`,
    lastModified: new Date(product.createdAt),
    changeFrequency: 'weekly' as const,
    priority: 0.9,
  }))

  // 获取所有集合
  const collectionRoutes = collectionsConfig.map((collection) => ({
    url: `https://introducing.day/collection/${collection.slug}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }))

  // 合并所有路由
  return [...baseRoutes, ...productRoutes, ...collectionRoutes]
}
