# 简化解析器系统文档

## 概述

简化的解析器系统专注于核心功能，提供简单可靠的产品信息提取。默认使用 Jina AI，失败时回退到 Firecrawl，最后使用基础解析。

## 解析器类型

### 1. Jina AI Parser (主要)
- **特点**: 快速、准确的文本提取
- **API**: Jina AI Reader API
- **配置**: 需要 `JINA_API_KEY` 环境变量

### 2. Firecrawl Parser (备用)
- **特点**: 支持LLM增强提取，能获取更详细的信息
- **API**: Firecrawl API
- **配置**: 需要 `FIRECRAWL_API_KEY` 环境变量

### 3. 基础解析 (最后回退)
- **特点**: 无需API，基于URL生成基础信息
- **配置**: 无需配置，总是可用

## 解析策略

系统使用简单的回退策略：
1. **Jina AI** - 首先尝试，适合大多数网站
2. **Firecrawl** - Jina 失败时使用，适合复杂网站
3. **基础解析** - 都失败时使用，确保总有结果

## 使用方法

### 基础使用（推荐）
```typescript
import { parseProductFromUrl } from '@/lib/submit/url-parser'

const productInfo = await parseProductFromUrl('https://example.com')
console.log(productInfo)
```

### 直接使用解析器
```typescript
import { simpleParser } from '@/lib/parsers'

const result = await simpleParser.parse('https://example.com')
console.log(result)
```

### 使用单个解析器
```typescript
import { JinaParser, FirecrawlParser } from '@/lib/parsers'

// 使用 Jina AI
const jinaParser = new JinaParser()
if (jinaParser.isAvailable()) {
  const result = await jinaParser.parse('https://example.com')
}

// 使用 Firecrawl
const firecrawlParser = new FirecrawlParser()
if (firecrawlParser.isAvailable()) {
  const result = await firecrawlParser.parse('https://example.com')
}
```

## 配置选项

### 环境变量
```bash
# API密钥（可选）
JINA_API_KEY=your_jina_api_key
FIRECRAWL_API_KEY=your_firecrawl_api_key
```

如果不配置API密钥，系统会自动使用基础解析。

## 解析结果

### 返回数据结构
```typescript
interface ParsedProductInfo {
  name?: string           // 产品名称
  tagline?: string        // 产品标语
  description?: string    // 产品描述
  logoUrl?: string        // Logo URL
  coverImageUrl?: string  // 封面图片 URL
  category?: string       // 产品分类
  tags?: string[]         // 标签列表
}
```

## 工作流程

1. **URL 输入** → 用户输入产品URL
2. **Jina AI 解析** → 首先尝试使用 Jina AI 提取信息
3. **Firecrawl 备用** → 如果 Jina 失败，使用 Firecrawl
4. **基础解析** → 如果都失败，生成基础信息
5. **返回结果** → 总是返回产品信息

## 文件结构

```
lib/parsers/
├── types.ts              # 类型定义
├── jina-parser.ts         # Jina AI 解析器
├── firecrawl-parser.ts    # Firecrawl 解析器
├── simple-parser.ts       # 简化解析管理器
└── index.ts               # 导出文件

lib/submit/
└── url-parser.ts          # URL解析入口
```

## 最佳实践

1. **API密钥管理**: 使用环境变量存储API密钥
2. **错误处理**: 系统自动处理解析失败的情况
3. **性能监控**: 查看控制台日志了解解析器使用情况

## 故障排除

### 常见问题
1. **API密钥无效**: 检查 `.env` 文件中的API密钥配置
2. **解析失败**: 系统会自动回退到基础解析，总是有结果
3. **结果质量差**: 确保配置了有效的API密钥

### 调试技巧
- 查看浏览器控制台日志
- 检查网络请求是否成功
- 验证API密钥是否正确
