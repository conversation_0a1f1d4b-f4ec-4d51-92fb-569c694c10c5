# 投票功能设置指南

## 概述

投票功能允许用户对产品进行点赞，包含以下特性：
- 用户认证集成
- 防止重复投票
- 实时投票计数更新
- 响应式 UI 设计
- 按照用户偏好，当投票数为 0 时显示 "Vote"

## 数据库结构

### 表结构

#### `product_votes` 表
存储每个产品的投票统计信息：
```sql
- id: SERIAL PRIMARY KEY
- product_id: INTEGER (外键关联 products.id)
- upvotes: INTEGER (点赞数)
- downvotes: INTEGER (点踩数，预留功能)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### `user_votes` 表
存储用户的投票记录，防止重复投票：
```sql
- id: SERIAL PRIMARY KEY
- product_id: INTEGER (外键关联 products.id)
- user_id: UUID (外键关联 auth.users.id)
- vote_type: VARCHAR(10) ('upvote' 或 'downvote')
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### 安全策略 (RLS)

- **product_votes**: 任何人可读，认证用户可写
- **user_votes**: 用户只能管理自己的投票记录

### 自动化功能

- **触发器**: 自动更新 `product_votes` 表的计数
- **时间戳**: 自动更新 `updated_at` 字段

## 设置步骤

### 1. 运行数据库迁移

```bash
# 方法 1: 使用提供的脚本
npx tsx scripts/run-voting-migration.ts

# 方法 2: 手动在 Supabase 控制台执行
# 复制 supabase/migrations/20250101_create_voting_tables.sql 的内容
# 在 Supabase 控制台的 SQL 编辑器中执行
```

### 2. 验证设置

在 Supabase 控制台中检查：
- `product_votes` 表已创建
- `user_votes` 表已创建
- RLS 策略已启用
- 触发器已创建

## 使用方法

### 在组件中使用 Vote 组件

```tsx
import { Vote } from "@/components/vote"

function ProductCard({ product }) {
  return (
    <div>
      <h3>{product.name}</h3>
      <Vote 
        productId={product.id} 
        initialUpvotes={product.upvotes || 0}
      />
    </div>
  )
}
```

### 获取产品投票数

```tsx
// 在服务器组件中
import { createSupabaseServerClient } from "@/lib/supabase/client"

async function getProductWithVotes(productId: number) {
  const supabase = createSupabaseServerClient()
  
  const { data: product } = await supabase
    .from('products')
    .select(`
      *,
      product_votes(upvotes, downvotes)
    `)
    .eq('id', productId)
    .single()
    
  return product
}
```

### 使用投票服务

```tsx
import { addVote, removeVote, getProductVotes, hasUserVoted } from "@/lib/services/vote-service"

// 添加投票
const result = await addVote(productId)
if (result.success) {
  console.log("投票成功")
}

// 移除投票
const result = await removeVote(productId)
if (result.success) {
  console.log("取消投票成功")
}

// 获取投票数
const voteCount = await getProductVotes(productId)

// 检查用户是否已投票
const hasVoted = await hasUserVoted(productId)
```

## 组件特性

### Vote 组件属性

```tsx
interface VoteProps {
  productId: number        // 产品 ID
  initialUpvotes?: number  // 初始投票数
  className?: string       // 自定义样式类
}
```

### 状态管理

- **登录检查**: 未登录用户点击时显示登录提示
- **投票状态**: 显示用户是否已投票
- **加载状态**: 防止重复点击
- **错误处理**: 显示友好的错误信息

### UI 特性

- **响应式设计**: 适配不同屏幕尺寸
- **无障碍支持**: 包含 ARIA 标签和键盘导航
- **视觉反馈**: 投票状态的颜色变化
- **用户偏好**: 投票数为 0 时显示 "Vote"

## 故障排除

### 常见问题

1. **表不存在错误**
   - 确保已运行数据库迁移
   - 检查 Supabase 控制台中的表结构

2. **权限错误**
   - 确保用户已登录
   - 检查 RLS 策略是否正确设置

3. **投票计数不更新**
   - 检查触发器是否正确创建
   - 查看数据库日志中的错误信息

### 调试技巧

```tsx
// 在浏览器控制台中检查投票状态
const supabase = createSupabaseClient()

// 查看产品投票数
const { data } = await supabase
  .from('product_votes')
  .select('*')
  .eq('product_id', YOUR_PRODUCT_ID)

// 查看用户投票记录
const { data } = await supabase
  .from('user_votes')
  .select('*')
  .eq('product_id', YOUR_PRODUCT_ID)
```

## 扩展功能

### 添加点踩功能

修改 Vote 组件以支持点踩：

```tsx
// 在 handleVote 函数中添加点踩逻辑
const voteType = isUpvote ? 'upvote' : 'downvote'

await supabase
  .from('user_votes')
  .insert({
    product_id: productId,
    user_id: userId,
    vote_type: voteType
  })
```

### 投票历史记录

查询用户的投票历史：

```tsx
const { data: userVotes } = await supabase
  .from('user_votes')
  .select(`
    *,
    products(name, tagline)
  `)
  .eq('user_id', userId)
  .order('created_at', { ascending: false })
```

## 性能优化

- 使用数据库索引提高查询性能
- 实现投票数据的缓存策略
- 考虑使用实时订阅更新投票计数
