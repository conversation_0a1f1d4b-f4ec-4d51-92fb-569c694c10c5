import type { ProductParser, ParseResult, ParsedProductInfo } from './types'

export class FirecrawlParser implements ProductParser {
  name = 'firecrawl'
  private apiKey: string | undefined
  private baseUrl = 'https://api.firecrawl.dev/v0'

  constructor() {
    this.apiKey = process.env.FIRECRAWL_API_KEY
  }

  isAvailable(): boolean {
    return !!this.apiKey
  }

  async parse(url: string): Promise<ParseResult> {
    const startTime = Date.now()

    try {
      if (!this.isAvailable()) {
        throw new Error('Firecrawl API key not configured')
      }

      const response = await fetch(`${this.baseUrl}/scrape`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          url: url,
          pageOptions: {
            onlyMainContent: true,
            includeHtml: false,
            screenshot: false
          },
          extractorOptions: {
            mode: 'llm-extraction',
            extractionPrompt: `Extract the following information from this webpage:
              - Product name
              - Product tagline/subtitle
              - Product description
              - Logo URL
              - Main image/screenshot URL
              - Product category
              - Tags/keywords

              Return as JSON with keys: name, tagline, description, logoUrl, coverImageUrl, category, tags`
          }
        }),
        signal: AbortSignal.timeout(45000)
      })

      if (!response.ok) {
        throw new Error(`Firecrawl API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Firecrawl parsing failed')
      }

      const parsedInfo = this.extractProductInfo(data, url)

      return {
        success: true,
        data: parsedInfo,
        source: this.name,
        duration: Date.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.name,
        duration: Date.now() - startTime
      }
    }
  }

  private extractProductInfo(data: any, url: string): ParsedProductInfo {
    // Firecrawl返回的数据结构
    const content = data.data?.content || ''
    const metadata = data.data?.metadata || {}
    const llmExtraction = data.data?.llm_extraction || {}

    // 优先使用LLM提取的结构化数据
    if (llmExtraction && typeof llmExtraction === 'object') {
      return {
        name: llmExtraction.name || metadata.title || this.generateNameFromUrl(url),
        tagline: llmExtraction.tagline || metadata.description?.substring(0, 100),
        description: llmExtraction.description || metadata.description,
        logoUrl: llmExtraction.logoUrl,
        coverImageUrl: llmExtraction.coverImageUrl || metadata.ogImage,
        category: llmExtraction.category || this.inferCategory(content),
        tags: Array.isArray(llmExtraction.tags) ? llmExtraction.tags : this.extractTags(content),
        features: Array.isArray(llmExtraction.features) ? llmExtraction.features : this.extractFeatures(content),
        price: llmExtraction.price
      }
    }

    // 回退到基础解析
    return {
      name: metadata.title || this.generateNameFromUrl(url),
      tagline: this.extractTagline(content),
      description: metadata.description || this.extractDescription(content),
      logoUrl: this.extractLogo(content),
      coverImageUrl: metadata.ogImage || this.extractCoverImage(content),
      category: this.inferCategory(content),
      tags: this.extractTags(content),
      features: this.extractFeatures(content)
    }
  }

  private extractTagline(content: string): string | undefined {
    // 从内容中提取标语
    const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0)

    // 寻找可能的标语（通常在标题后面，长度适中）
    for (let i = 1; i < Math.min(lines.length, 5); i++) {
      const line = lines[i]
      if (line.length > 20 && line.length < 150 && !line.includes('http')) {
        return line
      }
    }

    return undefined
  }

  private extractDescription(content: string): string | undefined {
    // 提取描述段落
    const paragraphs = content.split('\n\n').map(p => p.trim()).filter(p => p.length > 50)

    if (paragraphs.length > 0) {
      return paragraphs[0].substring(0, 500)
    }

    return undefined
  }

  private extractLogo(content: string): string | undefined {
    // 尝试从内容中找到logo相关的图片URL
    const logoPatterns = [
      /logo[^"\s]*\.(png|jpg|jpeg|svg|webp)/gi,
      /brand[^"\s]*\.(png|jpg|jpeg|svg|webp)/gi
    ]

    for (const pattern of logoPatterns) {
      const match = content.match(pattern)
      if (match) {
        return match[0]
      }
    }

    return undefined
  }

  private extractCoverImage(content: string): string | undefined {
    // 提取封面图片
    const imagePatterns = [
      /screenshot[^"\s]*\.(png|jpg|jpeg|webp)/gi,
      /hero[^"\s]*\.(png|jpg|jpeg|webp)/gi,
      /cover[^"\s]*\.(png|jpg|jpeg|webp)/gi
    ]

    for (const pattern of imagePatterns) {
      const match = content.match(pattern)
      if (match) {
        return match[0]
      }
    }

    return undefined
  }

  private inferCategory(content: string): string | undefined {
    const categories = {
      'AI': ['artificial intelligence', 'machine learning', 'ai powered', 'neural network', 'deep learning'],
      'Developer Tools': ['api', 'sdk', 'developer', 'code editor', 'programming', 'github', 'repository'],
      'Design': ['design tool', 'ui design', 'ux design', 'figma', 'sketch', 'prototype'],
      'Productivity': ['productivity', 'task management', 'todo', 'workflow', 'automation'],
      'Marketing': ['marketing tool', 'seo', 'analytics', 'campaign', 'email marketing'],
      'E-commerce': ['online store', 'ecommerce', 'payment', 'shopping cart', 'marketplace'],
      'Social': ['social media', 'community', 'chat', 'messaging', 'social network'],
      'Finance': ['fintech', 'payment', 'banking', 'cryptocurrency', 'investment'],
      'Health': ['health', 'fitness', 'medical', 'wellness', 'healthcare'],
      'Education': ['education', 'learning', 'course', 'tutorial', 'training']
    }

    const lowerContent = content.toLowerCase()

    for (const [category, keywords] of Object.entries(categories)) {
      const score = keywords.reduce((acc, keyword) => {
        return acc + (lowerContent.includes(keyword) ? 1 : 0)
      }, 0)

      if (score >= 2) { // 需要匹配至少2个关键词
        return category
      }
    }

    return undefined
  }

  private extractTags(content: string): string[] {
    const tags: string[] = []
    const lowerContent = content.toLowerCase()

    const tagKeywords = [
      'saas', 'mobile app', 'web app', 'api', 'open source', 'free tier',
      'subscription', 'b2b', 'b2c', 'startup', 'enterprise', 'chrome extension',
      'ios app', 'android app', 'mac app', 'windows app', 'cross-platform',
      'real-time', 'cloud-based', 'self-hosted', 'no-code', 'low-code'
    ]

    tagKeywords.forEach(tag => {
      if (lowerContent.includes(tag)) {
        tags.push(tag.split(' ').map(word =>
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' '))
      }
    })

    return [...new Set(tags)] // 去重
  }

  private extractFeatures(content: string): string[] {
    const features: string[] = []

    // 寻找特性列表的模式
    const featurePatterns = [
      /(?:features?|capabilities|benefits?):\s*\n((?:[-•*]\s*.+\n?)+)/gi,
      /(?:what (?:we|you) (?:offer|get|can do)):\s*\n((?:[-•*]\s*.+\n?)+)/gi
    ]

    for (const pattern of featurePatterns) {
      const matches = content.match(pattern)
      if (matches) {
        matches.forEach(match => {
          const items = match.split('\n')
            .map(item => item.replace(/^[-•*]\s*/, '').trim())
            .filter(item => item.length > 10 && item.length < 100)

          features.push(...items.slice(0, 5))
        })
      }
    }

    return [...new Set(features)].slice(0, 5) // 去重并限制数量
  }

  private generateNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
      const domain = urlObj.hostname.replace('www.', '')
      const domainParts = domain.split('.')
      return domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')
    } catch {
      return 'Unknown Product'
    }
  }
}
