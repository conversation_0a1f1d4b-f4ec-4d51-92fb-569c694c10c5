import type { ProductParser, ParseResult, ParsedProductInfo } from './types'

export class FirecrawlParser implements ProductParser {
  name = 'firecrawl'
  private apiKey: string | undefined
  private baseUrl = 'https://api.firecrawl.dev/v1'

  constructor() {
    this.apiKey = process.env.FIRECRAWL_API_KEY
  }

  isAvailable(): boolean {
    return !!this.apiKey
  }

  async parse(url: string): Promise<ParseResult> {
    const startTime = Date.now()

    try {
      if (!this.isAvailable()) {
        throw new Error('Firecrawl API key not configured')
      }

      const response = await fetch(`${this.baseUrl}/scrape`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          url: url,
          formats: ['markdown'],
          onlyMainContent: true
        }),
        signal: AbortSignal.timeout(45000)
      })

      if (!response.ok) {
        throw new Error(`Firecrawl API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Firecrawl parsing failed')
      }

      const parsedInfo = this.extractProductInfo(data, url)

      return {
        success: true,
        data: parsedInfo,
        source: this.name,
        duration: Date.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.name,
        duration: Date.now() - startTime
      }
    }
  }

  private extractProductInfo(data: any, url: string): ParsedProductInfo {
    // Firecrawl v1 API 返回的数据结构
    const content = data.markdown || data.html || ''
    const metadata = data.metadata || {}
    const extractedData = data.extract || {}

    // 优先使用提取的结构化数据
    if (extractedData && typeof extractedData === 'object') {
      return {
        name: extractedData.name || metadata.title || this.generateNameFromUrl(url),
        tagline: extractedData.tagline || metadata.description?.substring(0, 100),
        description: extractedData.description || metadata.description,
        logoUrl: extractedData.logoUrl,
        coverImageUrl: extractedData.coverImageUrl || metadata.ogImage
      }
    }

    // 回退到基础解析
    return {
      name: metadata.title || this.generateNameFromUrl(url),
      tagline: this.extractTagline(content),
      description: metadata.description || this.extractDescription(content),
      logoUrl: this.extractLogo(content),
      coverImageUrl: metadata.ogImage || this.extractCoverImage(content)
    }
  }

  private extractTagline(content: string): string | undefined {
    // 从内容中提取标语
    const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0)

    // 寻找可能的标语（通常在标题后面，长度适中）
    for (let i = 1; i < Math.min(lines.length, 5); i++) {
      const line = lines[i]
      if (line.length > 20 && line.length < 150 && !line.includes('http')) {
        return line
      }
    }

    return undefined
  }

  private extractDescription(content: string): string | undefined {
    // 提取描述段落
    const paragraphs = content.split('\n\n').map(p => p.trim()).filter(p => p.length > 50)

    if (paragraphs.length > 0) {
      return paragraphs[0].substring(0, 500)
    }

    return undefined
  }

  private extractLogo(content: string): string | undefined {
    // 尝试从内容中找到logo相关的图片URL
    const logoPatterns = [
      /logo[^"\s]*\.(png|jpg|jpeg|svg|webp)/gi,
      /brand[^"\s]*\.(png|jpg|jpeg|svg|webp)/gi
    ]

    for (const pattern of logoPatterns) {
      const match = content.match(pattern)
      if (match) {
        return match[0]
      }
    }

    return undefined
  }

  private extractCoverImage(content: string): string | undefined {
    // 提取封面图片
    const imagePatterns = [
      /screenshot[^"\s]*\.(png|jpg|jpeg|webp)/gi,
      /hero[^"\s]*\.(png|jpg|jpeg|webp)/gi,
      /cover[^"\s]*\.(png|jpg|jpeg|webp)/gi
    ]

    for (const pattern of imagePatterns) {
      const match = content.match(pattern)
      if (match) {
        return match[0]
      }
    }

    return undefined
  }

  private generateNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
      const domain = urlObj.hostname.replace('www.', '')
      const domainParts = domain.split('.')
      return domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')
    } catch {
      return 'Unknown Product'
    }
  }
}
