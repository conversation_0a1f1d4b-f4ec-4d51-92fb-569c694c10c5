// 解析器返回的产品信息接口
export interface ParsedProductInfo {
  name?: string
  tagline?: string
  description?: string
  logoUrl?: string
  coverImageUrl?: string
  category?: string
  tags?: string[]
}

// 解析器结果接口
export interface ParseResult {
  success: boolean
  data?: ParsedProductInfo
  error?: string
  source: string // 解析器名称
  duration: number // 解析耗时（毫秒）
}

// 解析器接口
export interface ProductParser {
  name: string
  parse(url: string): Promise<ParseResult>
  isAvailable(): boolean // 检查解析器是否可用（API密钥等）
}
