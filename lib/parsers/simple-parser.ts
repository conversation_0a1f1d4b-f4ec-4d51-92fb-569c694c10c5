import type { ParsedProductInfo, ParseResult } from './types'

/**
 * 简化的解析器管理器
 * 默认使用 Jina AI，如果失败则回退到基础解析
 */
export class SimpleParser {
  private jinaParser: any = null
  private firecrawlParser: any = null

  async parse(url: string): Promise<ParsedProductInfo> {
    // 首先尝试 Jina AI
    try {
      if (!this.jinaParser) {
        const { JinaParser } = await import('./jina-parser')
        this.jinaParser = new JinaParser()
      }

      if (this.jinaParser.isAvailable()) {
        const result = await this.jinaParser.parse(url)
        if (result.success && result.data) {
          console.log(`Successfully parsed with Jina AI in ${result.duration}ms`)
          return result.data
        }
      }
    } catch (error) {
      console.warn('Jina AI parsing failed:', error)
    }

    // 如果 Jina 失败，尝试 Firecrawl
    try {
      if (!this.firecrawlParser) {
        const { FirecrawlParser } = await import('./firecrawl-parser')
        this.firecrawlParser = new FirecrawlParser()
      }

      if (this.firecrawlParser.isAvailable()) {
        const result = await this.firecrawlParser.parse(url)
        if (result.success && result.data) {
          console.log(`Successfully parsed with Firecrawl in ${result.duration}ms`)
          return result.data
        }
      }
    } catch (error) {
      console.warn('Firecrawl parsing failed:', error)
    }

    // 如果都失败，返回基础信息
    console.log('All parsers failed, using basic info')
    return this.generateBasicInfo(url)
  }

  private generateBasicInfo(url: string): ParsedProductInfo {
    try {
      const normalizedUrl = this.normalizeUrl(url)
      const urlObj = new URL(normalizedUrl)
      const domain = urlObj.hostname.replace('www.', '')

      // 从域名生成产品名称
      const domainParts = domain.split('.')
      const productName = domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')

      return {
        name: productName,
        tagline: `Discover ${productName}`,
        description: `Learn more about ${productName} and what makes it special.`,
        category: this.inferCategoryFromDomain(domain),
        tags: this.generateBasicTags(domain)
      }
    } catch (error) {
      return {
        name: 'Unknown Product',
        tagline: 'A new product to discover',
        description: 'This product is waiting to be explored.'
      }
    }
  }

  private normalizeUrl(url: string): string {
    if (!url) return ''

    // Remove trailing slash
    url = url.replace(/\/$/, '')

    // Add https if no protocol
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `https://${url}`
    }

    return url
  }

  private inferCategoryFromDomain(domain: string): string | undefined {
    const categoryKeywords = {
      'AI': ['ai', 'ml', 'neural', 'bot', 'gpt', 'llm'],
      'Developer Tools': ['api', 'dev', 'code', 'git', 'sdk', 'cli'],
      'Design': ['design', 'ui', 'ux', 'figma', 'sketch', 'creative'],
      'Productivity': ['task', 'todo', 'note', 'plan', 'organize', 'workflow'],
      'Marketing': ['market', 'seo', 'analytics', 'campaign', 'ads'],
      'E-commerce': ['shop', 'store', 'cart', 'pay', 'commerce', 'sell'],
      'Social': ['social', 'chat', 'community', 'connect', 'share'],
      'Finance': ['pay', 'bank', 'finance', 'money', 'crypto', 'invest'],
      'Health': ['health', 'fit', 'medical', 'wellness', 'care'],
      'Education': ['learn', 'edu', 'course', 'teach', 'study', 'academy']
    }

    const lowerDomain = domain.toLowerCase()

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some(keyword => lowerDomain.includes(keyword))) {
        return category
      }
    }

    return undefined
  }

  private generateBasicTags(domain: string): string[] {
    const tags: string[] = []
    const lowerDomain = domain.toLowerCase()

    // 基于域名特征生成标签
    const tagMappings = {
      'app': 'Mobile App',
      'api': 'API',
      'saas': 'SaaS',
      'tool': 'Tool',
      'platform': 'Platform',
      'service': 'Service',
      'cloud': 'Cloud',
      'web': 'Web App',
      'mobile': 'Mobile',
      'enterprise': 'Enterprise',
      'startup': 'Startup',
      'free': 'Free',
      'pro': 'Professional'
    }

    for (const [keyword, tag] of Object.entries(tagMappings)) {
      if (lowerDomain.includes(keyword)) {
        tags.push(tag)
      }
    }

    // 添加一些通用标签
    if (tags.length === 0) {
      tags.push('Web App', 'SaaS')
    }

    return tags
  }
}

// 创建单例实例
const simpleParser = new SimpleParser()

export { simpleParser }
