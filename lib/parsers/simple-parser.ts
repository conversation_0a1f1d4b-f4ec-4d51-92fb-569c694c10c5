import type { ParsedProductInfo, ParseResult } from './types'

/**
 * 简化的解析器管理器
 * 默认使用 Firecrawl，如果失败则回退到 Jina AI，最后回退到基础解析
 */
export class SimpleParser {
  private jinaParser: any = null
  private firecrawlParser: any = null

  async parse(url: string): Promise<ParsedProductInfo> {
    // 首先尝试 Firecrawl
    try {
      if (!this.firecrawlParser) {
        const { FirecrawlParser } = await import('./firecrawl-parser')
        this.firecrawlParser = new FirecrawlParser()
      }

      if (this.firecrawlParser.isAvailable()) {
        const result = await this.firecrawlParser.parse(url)
        if (result.success && result.data) {
          console.log(`Successfully parsed with Firecrawl in ${result.duration}ms`)
          return result.data
        }
      }
    } catch (error) {
      console.warn('Firecrawl parsing failed:', error)
    }

    // 如果 Firecrawl 失败，尝试 Jina AI
    try {
      if (!this.jinaParser) {
        const { Jina<PERSON>arser } = await import('./jina-parser')
        this.jinaParser = new JinaParser()
      }

      if (this.jinaParser.isAvailable()) {
        const result = await this.jinaParser.parse(url)
        if (result.success && result.data) {
          console.log(`Successfully parsed with Jina AI in ${result.duration}ms`)
          return result.data
        }
      }
    } catch (error) {
      console.warn('Jina AI parsing failed:', error)
    }

    // 如果都失败，返回基础信息
    console.log('All parsers failed, using basic info')
    return this.generateBasicInfo(url)
  }

  private generateBasicInfo(url: string): ParsedProductInfo {
    try {
      const normalizedUrl = this.normalizeUrl(url)
      const urlObj = new URL(normalizedUrl)
      const domain = urlObj.hostname.replace('www.', '')

      // 从域名生成产品名称
      const domainParts = domain.split('.')
      const productName = domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')

      return {
        name: productName,
        tagline: `Discover ${productName}`,
        description: `Learn more about ${productName} and what makes it special.`
      }
    } catch (error) {
      return {
        name: 'Unknown Product',
        tagline: 'A new product to discover',
        description: 'This product is waiting to be explored.'
      }
    }
  }

  private normalizeUrl(url: string): string {
    if (!url) return ''

    // Remove trailing slash
    url = url.replace(/\/$/, '')

    // Add https if no protocol
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `https://${url}`
    }

    return url
  }
}

// 创建单例实例
const simpleParser = new SimpleParser()

export { simpleParser }
