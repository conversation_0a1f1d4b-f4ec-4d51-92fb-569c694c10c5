import type { ProductParser, ParseResult, ParsedProductInfo } from './types'

export class JinaParser implements ProductParser {
  name = 'jina'
  private apiKey: string | undefined
  private baseUrl = 'https://r.jina.ai'

  constructor() {
    this.apiKey = process.env.JINA_API_KEY
  }

  isAvailable(): boolean {
    return !!this.apiKey
  }

  async parse(url: string): Promise<ParseResult> {
    const startTime = Date.now()

    try {
      if (!this.isAvailable()) {
        throw new Error('Jina API key not configured')
      }

      const response = await fetch(`${this.baseUrl}/${url}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Accept': 'application/json',
          'User-Agent': 'AutoParser/1.0'
        },
        signal: AbortSignal.timeout(30000)
      })

      if (!response.ok) {
        throw new Error(`Jina API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      const parsedInfo = this.extractProductInfo(data, url)

      return {
        success: true,
        data: parsedInfo,
        source: this.name,
        duration: Date.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.name,
        duration: Date.now() - startTime
      }
    }
  }

  private extractProductInfo(data: any, url: string): ParsedProductInfo {
    // Jina AI 返回的数据结构: { data: { title, description, content, url } }
    const jinaData = data.data || data
    const content = jinaData.content || jinaData.text || ''
    const title = jinaData.title || this.extractTitle(content)
    const description = jinaData.description || this.extractDescription(content)

    // 清理标题，移除网站名称
    const cleanTitle = this.cleanTitle(title)

    return {
      name: cleanTitle || this.generateNameFromUrl(url),
      tagline: this.extractTagline(content, description),
      description: description,
      logoUrl: this.extractLogo(content),
      coverImageUrl: this.extractCoverImage(content),
      category: this.inferCategory(content),
      tags: this.extractTags(content)
    }
  }

  private extractTitle(content: string): string | undefined {
    // 尝试从内容中提取标题
    const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i)
    if (titleMatch) {
      return titleMatch[1].trim()
    }

    // 尝试从h1标签提取
    const h1Match = content.match(/<h1[^>]*>([^<]+)<\/h1>/i)
    if (h1Match) {
      return h1Match[1].trim()
    }

    return undefined
  }

  private extractDescription(content: string): string | undefined {
    // 尝试从meta description提取
    const metaMatch = content.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (metaMatch) {
      return metaMatch[1].trim()
    }

    return undefined
  }

  private cleanTitle(title: string | undefined): string | undefined {
    if (!title) return undefined

    // 移除常见的网站后缀
    const cleanedTitle = title
      .replace(/\s*\|\s*.+$/, '') // 移除 "| 网站名称" 部分
      .replace(/\s*-\s*.+$/, '') // 移除 "- 网站名称" 部分
      .replace(/\s*·\s*.+$/, '') // 移除 "· 网站名称" 部分
      .replace(/\s*—\s*.+$/, '') // 移除 "— 网站名称" 部分
      .trim()

    return cleanedTitle || title
  }

  private extractTagline(content: string, description?: string): string | undefined {
    // 如果有 meta description，优先使用（但要简短）
    if (description && description.length > 20 && description.length < 150) {
      return description
    }

    // 尝试提取标语/副标题
    const h2Match = content.match(/<h2[^>]*>([^<]+)<\/h2>/i)
    if (h2Match) {
      return h2Match[1].trim()
    }

    // 尝试从第一段文字提取
    const firstParagraph = content.split('\n').find(line =>
      line.trim().length > 20 &&
      line.trim().length < 150 &&
      !line.includes('http') &&
      !line.includes('=') &&
      !line.includes('<')
    )

    return firstParagraph?.trim()
  }

  private extractLogo(content: string): string | undefined {
    // 尝试从内容中提取logo URL
    const logoMatches = content.match(/src=["\']([^"']*logo[^"']*)["\']|href=["\']([^"']*logo[^"']*)["\']/gi)
    if (logoMatches && logoMatches.length > 0) {
      const logoMatch = logoMatches[0].match(/["\']([^"']+)["\']/)
      if (logoMatch) {
        return logoMatch[1]
      }
    }

    return undefined
  }

  private extractCoverImage(content: string): string | undefined {
    // 尝试从内容中提取第一个图片
    const imgMatch = content.match(/<img[^>]*src=["\']([^"']+)["\'][^>]*>/i)
    if (imgMatch) {
      return imgMatch[1]
    }

    return undefined
  }

  private inferCategory(content: string): string | undefined {
    const categories = {
      'AI': ['artificial intelligence', 'machine learning', 'ai', 'ml', 'neural'],
      'Developer Tools': ['api', 'sdk', 'developer', 'code', 'programming'],
      'Design': ['design', 'ui', 'ux', 'figma', 'sketch'],
      'Productivity': ['productivity', 'task', 'todo', 'workflow'],
      'Marketing': ['marketing', 'seo', 'analytics', 'campaign'],
      'E-commerce': ['shop', 'store', 'ecommerce', 'payment', 'cart']
    }

    const lowerContent = content.toLowerCase()

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => lowerContent.includes(keyword))) {
        return category
      }
    }

    return undefined
  }

  private extractTags(content: string): string[] {
    const tags: string[] = []
    const lowerContent = content.toLowerCase()

    const commonTags = [
      'saas', 'mobile app', 'web app', 'api', 'open source',
      'free', 'subscription', 'b2b', 'b2c', 'startup'
    ]

    commonTags.forEach(tag => {
      if (lowerContent.includes(tag.toLowerCase())) {
        tags.push(tag)
      }
    })

    return tags
  }

  private extractFeatures(content: string): string[] {
    // 尝试提取功能列表
    const features: string[] = []
    const listMatches = content.match(/<li[^>]*>([^<]+)<\/li>/gi)

    if (listMatches) {
      listMatches.slice(0, 5).forEach(match => {
        const feature = match.replace(/<[^>]*>/g, '').trim()
        if (feature.length > 10 && feature.length < 100) {
          features.push(feature)
        }
      })
    }

    return features
  }

  private generateNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
      const domain = urlObj.hostname.replace('www.', '')
      const domainParts = domain.split('.')
      return domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')
    } catch {
      return 'Unknown Product'
    }
  }
}
