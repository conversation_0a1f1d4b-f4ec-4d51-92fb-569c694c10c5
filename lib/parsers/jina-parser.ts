import type { ProductParser, ParseResult, ParsedProductInfo } from './types'

export class JinaParser implements ProductParser {
  name = 'jina'
  private apiKey: string | undefined
  private baseUrl = 'https://r.jina.ai'

  constructor() {
    this.apiKey = process.env.JINA_API_KEY
  }

  isAvailable(): boolean {
    return !!this.apiKey
  }

  async parse(url: string): Promise<ParseResult> {
    const startTime = Date.now()

    try {
      if (!this.isAvailable()) {
        throw new Error('Jina API key not configured')
      }

      const response = await fetch(`${this.baseUrl}/${url}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Accept': 'application/json',
          'User-Agent': 'AutoParser/1.0',
          'X-Return-Format': 'html'
        },
        signal: AbortSignal.timeout(30000)
      })

      if (!response.ok) {
        throw new Error(`Jina API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      const parsedInfo = this.extractProductInfo(data, url)

      return {
        success: true,
        data: parsedInfo,
        source: this.name,
        duration: Date.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.name,
        duration: Date.now() - startTime
      }
    }
  }

  private extractProductInfo(data: any, url: string): ParsedProductInfo {
    // Jina AI 返回的数据结构: { data: { title, description, content, url, html } }
    const jinaData = data.data || data
    const htmlContent = jinaData.html || ''
    const textContent = jinaData.content || jinaData.text || ''
    const title = jinaData.title || this.extractTitle(htmlContent)
    const metaDescription = jinaData.description || this.extractDescription(htmlContent)

    // 调试：打印原始数据以了解结构
    console.log('Jina AI raw data keys:', Object.keys(jinaData))
    console.log('HTML content length:', htmlContent.length)
    console.log('Text content length:', textContent.length)
    console.log('HTML preview:', htmlContent.substring(0, 500))

    // 清理标题，移除网站名称
    const cleanTitle = this.cleanTitle(title)

    // 提取更丰富的描述信息
    const richDescription = this.extractRichDescription(htmlContent, metaDescription)

    // 提取图片信息
    const logoUrl = this.extractFavicon(htmlContent, url)
    const coverImageUrl = this.extractOgImage(htmlContent, jinaData)

    console.log('Extracted logo:', logoUrl)
    console.log('Extracted cover image:', coverImageUrl)

    return {
      name: cleanTitle || this.generateNameFromUrl(url),
      tagline: this.extractTagline(htmlContent, metaDescription),
      description: richDescription,
      logoUrl: logoUrl,
      coverImageUrl: coverImageUrl,
      category: this.inferCategory(textContent || htmlContent),
      tags: this.extractTags(textContent || htmlContent)
    }
  }

  private extractTitle(content: string): string | undefined {
    // 尝试从内容中提取标题
    const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i)
    if (titleMatch) {
      return titleMatch[1].trim()
    }

    // 尝试从h1标签提取
    const h1Match = content.match(/<h1[^>]*>([^<]+)<\/h1>/i)
    if (h1Match) {
      return h1Match[1].trim()
    }

    return undefined
  }

  private extractDescription(content: string): string | undefined {
    // 尝试从meta description提取
    const metaMatch = content.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (metaMatch) {
      return metaMatch[1].trim()
    }

    return undefined
  }

  private extractRichDescription(content: string, metaDescription?: string): string | undefined {
    // 如果有 meta description 且足够详细，优先使用
    if (metaDescription && metaDescription.length > 50) {
      return metaDescription
    }

    // 尝试从多个来源提取更丰富的描述
    const descriptions: string[] = []

    // 1. Meta description
    if (metaDescription) {
      descriptions.push(metaDescription)
    }

    // 2. 尝试从 og:description 提取
    const ogDescMatch = content.match(/<meta[^>]*property=["\']og:description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (ogDescMatch) {
      descriptions.push(ogDescMatch[1].trim())
    }

    // 3. 尝试从第一个段落提取
    const paragraphMatches = content.match(/<p[^>]*>([^<]+(?:<[^>]*>[^<]*)*)<\/p>/gi)
    if (paragraphMatches) {
      for (const match of paragraphMatches.slice(0, 3)) {
        const text = match.replace(/<[^>]*>/g, '').trim()
        if (text.length > 50 && text.length < 300 && !text.includes('cookie') && !text.includes('privacy')) {
          descriptions.push(text)
        }
      }
    }

    // 4. 尝试从主要内容区域提取
    const mainContentMatch = content.match(/<main[^>]*>([\s\S]*?)<\/main>/i) ||
                             content.match(/<article[^>]*>([\s\S]*?)<\/article>/i) ||
                             content.match(/<section[^>]*class=["\'][^"']*hero[^"']*["\'][^>]*>([\s\S]*?)<\/section>/i)

    if (mainContentMatch) {
      const mainText = mainContentMatch[1].replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()
      const sentences = mainText.split(/[.!?]+/).filter(s => s.trim().length > 30)
      if (sentences.length > 0) {
        const firstSentences = sentences.slice(0, 2).join('. ').trim()
        if (firstSentences.length > 50 && firstSentences.length < 400) {
          descriptions.push(firstSentences + (firstSentences.endsWith('.') ? '' : '.'))
        }
      }
    }

    // 选择最好的描述（优先选择长度适中且信息丰富的）
    const bestDescription = descriptions.find(desc => desc.length > 100 && desc.length < 300) ||
                           descriptions.find(desc => desc.length > 50 && desc.length < 500) ||
                           descriptions[0]

    return bestDescription
  }

  private cleanTitle(title: string | undefined): string | undefined {
    if (!title) return undefined

    // 移除常见的网站后缀和冗余信息
    let cleanedTitle = title
      .replace(/\s*\|\s*.+$/, '') // 移除 "| 网站名称" 部分
      .replace(/\s*-\s*.+$/, '') // 移除 "- 网站名称" 部分
      .replace(/\s*·\s*.+$/, '') // 移除 "· 网站名称" 部分
      .replace(/\s*—\s*.+$/, '') // 移除 "— 网站名称" 部分
      .replace(/\s*:\s*.+$/, '') // 移除 ": 描述" 部分
      .replace(/\s*–\s*.+$/, '') // 移除 "– 描述" 部分
      .trim()

    // 如果清理后的标题太短，尝试保留第一部分
    if (cleanedTitle.length < 3 && title.includes(':')) {
      const parts = title.split(':')
      cleanedTitle = parts[0].trim()
    }

    // 如果标题仍然太长，截取前面的关键部分
    if (cleanedTitle.length > 50) {
      const words = cleanedTitle.split(' ')
      if (words.length > 5) {
        cleanedTitle = words.slice(0, 5).join(' ')
      }
    }

    return cleanedTitle || title
  }

  private extractTagline(content: string, description?: string): string | undefined {
    // 如果有 meta description，优先使用（但要简短）
    if (description && description.length > 20 && description.length < 150) {
      return description
    }

    // 尝试提取标语/副标题
    const h2Match = content.match(/<h2[^>]*>([^<]+)<\/h2>/i)
    if (h2Match) {
      return h2Match[1].trim()
    }

    // 尝试从第一段文字提取
    const firstParagraph = content.split('\n').find(line =>
      line.trim().length > 20 &&
      line.trim().length < 150 &&
      !line.includes('http') &&
      !line.includes('=') &&
      !line.includes('<')
    )

    return firstParagraph?.trim()
  }

  private extractFavicon(content: string, url: string): string | undefined {
    const baseUrl = new URL(url).origin

    // 1. 尝试从 link rel="icon" 提取
    const iconMatches = [
      // 标准 favicon
      content.match(/<link[^>]*rel=["\'](?:icon|shortcut icon)["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i),
      // Apple touch icon
      content.match(/<link[^>]*rel=["\']apple-touch-icon["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i),
      // 其他图标格式
      content.match(/<link[^>]*rel=["\']icon["\'][^>]*type=["\']image\/[^"']*["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i)
    ]

    for (const match of iconMatches) {
      if (match && match[1]) {
        const iconUrl = match[1]
        // 处理相对路径
        if (iconUrl.startsWith('//')) {
          return `https:${iconUrl}`
        } else if (iconUrl.startsWith('/')) {
          return `${baseUrl}${iconUrl}`
        } else if (iconUrl.startsWith('http')) {
          return iconUrl
        } else {
          return `${baseUrl}/${iconUrl}`
        }
      }
    }

    // 2. 回退到默认 favicon 路径
    return `${baseUrl}/favicon.ico`
  }

  private extractOgImage(content: string, jinaData?: any): string | undefined {
    // 1. 尝试从 og:image 提取
    const ogImageMatch = content.match(/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (ogImageMatch) {
      return ogImageMatch[1]
    }

    // 2. 尝试从 twitter:image 提取
    const twitterImageMatch = content.match(/<meta[^>]*name=["\']twitter:image["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (twitterImageMatch) {
      return twitterImageMatch[1]
    }

    // 3. 尝试从第一个有意义的图片提取（避免小图标）
    const imgMatches = content.match(/<img[^>]*src=["\']([^"']+)["\'][^>]*>/gi)
    if (imgMatches) {
      for (const imgMatch of imgMatches) {
        const srcMatch = imgMatch.match(/src=["\']([^"']+)["\']/)
        if (srcMatch) {
          const src = srcMatch[1]
          // 过滤掉小图标和明显的装饰性图片
          if (!src.includes('icon') &&
              !src.includes('logo') &&
              !src.includes('favicon') &&
              !src.includes('sprite') &&
              !src.includes('pixel') &&
              !src.endsWith('.svg') &&
              (src.includes('screenshot') ||
               src.includes('preview') ||
               src.includes('hero') ||
               src.includes('banner') ||
               imgMatch.includes('width') && !imgMatch.includes('width="16"') && !imgMatch.includes('width="32"'))) {
            return src
          }
        }
      }
    }

    return undefined
  }

  private inferCategory(content: string): string | undefined {
    const categories = {
      'AI': ['artificial intelligence', 'machine learning', 'ai', 'ml', 'neural'],
      'Developer Tools': ['api', 'sdk', 'developer', 'code', 'programming'],
      'Design': ['design', 'ui', 'ux', 'figma', 'sketch'],
      'Productivity': ['productivity', 'task', 'todo', 'workflow'],
      'Marketing': ['marketing', 'seo', 'analytics', 'campaign'],
      'E-commerce': ['shop', 'store', 'ecommerce', 'payment', 'cart']
    }

    const lowerContent = content.toLowerCase()

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => lowerContent.includes(keyword))) {
        return category
      }
    }

    return undefined
  }

  private extractTags(content: string): string[] {
    const tags: string[] = []
    const lowerContent = content.toLowerCase()

    const commonTags = [
      'saas', 'mobile app', 'web app', 'api', 'open source',
      'free', 'subscription', 'b2b', 'b2c', 'startup'
    ]

    commonTags.forEach(tag => {
      if (lowerContent.includes(tag.toLowerCase())) {
        tags.push(tag)
      }
    })

    return tags
  }

  private extractFeatures(content: string): string[] {
    // 尝试提取功能列表
    const features: string[] = []
    const listMatches = content.match(/<li[^>]*>([^<]+)<\/li>/gi)

    if (listMatches) {
      listMatches.slice(0, 5).forEach(match => {
        const feature = match.replace(/<[^>]*>/g, '').trim()
        if (feature.length > 10 && feature.length < 100) {
          features.push(feature)
        }
      })
    }

    return features
  }

  private generateNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
      const domain = urlObj.hostname.replace('www.', '')
      const domainParts = domain.split('.')
      return domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')
    } catch {
      return 'Unknown Product'
    }
  }
}
