import type { ProductParser, ParseResult, ParsedProductInfo } from './types'

export class JinaParser implements ProductParser {
  name = 'jina'
  private apiKey: string | undefined
  private baseUrl = 'https://r.jina.ai'

  constructor() {
    this.apiKey = process.env.JINA_API_KEY
  }

  isAvailable(): boolean {
    return !!this.apiKey
  }

  async parse(url: string): Promise<ParseResult> {
    const startTime = Date.now()

    try {
      if (!this.isAvailable()) {
        throw new Error('Jina API key not configured')
      }

      const response = await fetch(`${this.baseUrl}/${url}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Accept': 'application/json',
          'User-Agent': 'AutoParser/1.0',
          'X-Return-Format': 'html'
        },
        signal: AbortSignal.timeout(30000)
      })

      if (!response.ok) {
        throw new Error(`Jina API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      const parsedInfo = this.extractProductInfo(data, url)

      return {
        success: true,
        data: parsedInfo,
        source: this.name,
        duration: Date.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.name,
        duration: Date.now() - startTime
      }
    }
  }

  private extractProductInfo(data: any, url: string): ParsedProductInfo {
    // Jina AI 返回的数据结构: { data: { title, description, content, url, html } }
    const jinaData = data.data || data
    const htmlContent = jinaData.html || ''

    const title = jinaData.title || this.extractTitle(htmlContent)
    const metaDescription = jinaData.description || this.extractDescription(htmlContent)

    // 清理标题，移除网站名称
    const cleanTitle = this.cleanTitle(title)

    // 提取更丰富的描述信息
    const richDescription = this.extractRichDescription(htmlContent, metaDescription)

    // 提取图片信息
    const logoUrl = this.extractFavicon(htmlContent, url)
    const coverImageUrl = this.extractOgImage(htmlContent, jinaData)

    return {
      name: cleanTitle || this.generateNameFromUrl(url),
      tagline: this.extractTagline(htmlContent, metaDescription),
      description: richDescription,
      logoUrl: logoUrl,
      coverImageUrl: coverImageUrl
    }
  }

  private extractTitle(content: string): string | undefined {
    // 尝试从内容中提取标题
    const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i)
    if (titleMatch) {
      return titleMatch[1].trim()
    }

    // 尝试从h1标签提取
    const h1Match = content.match(/<h1[^>]*>([^<]+)<\/h1>/i)
    if (h1Match) {
      return h1Match[1].trim()
    }

    return undefined
  }

  private extractDescription(content: string): string | undefined {
    // 尝试从meta description提取
    const metaMatch = content.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (metaMatch) {
      return metaMatch[1].trim()
    }

    return undefined
  }

  private extractRichDescription(content: string, metaDescription?: string): string | undefined {
    // 如果有 meta description 且足够详细，优先使用
    if (metaDescription && metaDescription.length > 50) {
      return metaDescription
    }

    // 尝试从多个来源提取更丰富的描述
    const descriptions: string[] = []

    // 1. Meta description
    if (metaDescription) {
      descriptions.push(metaDescription)
    }

    // 2. 尝试从 og:description 提取
    const ogDescMatch = content.match(/<meta[^>]*property=["\']og:description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (ogDescMatch) {
      descriptions.push(ogDescMatch[1].trim())
    }

    // 3. 尝试从第一个段落提取
    const paragraphMatches = content.match(/<p[^>]*>([^<]+(?:<[^>]*>[^<]*)*)<\/p>/gi)
    if (paragraphMatches) {
      for (const match of paragraphMatches.slice(0, 3)) {
        const text = match.replace(/<[^>]*>/g, '').trim()
        if (text.length > 50 && text.length < 300 && !text.includes('cookie') && !text.includes('privacy')) {
          descriptions.push(text)
        }
      }
    }

    // 4. 尝试从主要内容区域提取
    const mainContentMatch = content.match(/<main[^>]*>([\s\S]*?)<\/main>/i) ||
                             content.match(/<article[^>]*>([\s\S]*?)<\/article>/i) ||
                             content.match(/<section[^>]*class=["\'][^"']*hero[^"']*["\'][^>]*>([\s\S]*?)<\/section>/i)

    if (mainContentMatch) {
      const mainText = mainContentMatch[1].replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()
      const sentences = mainText.split(/[.!?]+/).filter(s => s.trim().length > 30)
      if (sentences.length > 0) {
        const firstSentences = sentences.slice(0, 2).join('. ').trim()
        if (firstSentences.length > 50 && firstSentences.length < 400) {
          descriptions.push(firstSentences + (firstSentences.endsWith('.') ? '' : '.'))
        }
      }
    }

    // 选择最好的描述（优先选择长度适中且信息丰富的）
    const bestDescription = descriptions.find(desc => desc.length > 100 && desc.length < 300) ||
                           descriptions.find(desc => desc.length > 50 && desc.length < 500) ||
                           descriptions[0]

    return bestDescription
  }

  private cleanTitle(title: string | undefined): string | undefined {
    if (!title) return undefined

    // 首先尝试智能清理，保留产品名称
    let cleanedTitle = title.trim()

    // 移除常见的网站后缀模式，但要更智能
    const cleaningPatterns = [
      // 移除 "| 网站名称" 但保留产品名称
      { pattern: /\s*\|\s*(.+)$/, shouldRemove: (match: string, productName: string) => {
        const afterPipe = match.split('|')[1].trim()
        // 如果管道后面是网站名称或通用描述，则移除
        return afterPipe.toLowerCase().includes('website') ||
               afterPipe.toLowerCase().includes('official') ||
               afterPipe.toLowerCase().includes('home') ||
               afterPipe.toLowerCase().includes('page') ||
               afterPipe.length > productName.length * 1.5
      }},

      // 移除 "- 网站名称" 但保留产品名称
      { pattern: /\s*-\s*(.+)$/, shouldRemove: (match: string, productName: string) => {
        const afterDash = match.split('-').slice(1).join('-').trim()
        return afterDash.toLowerCase().includes('official') ||
               afterDash.toLowerCase().includes('website') ||
               afterDash.length > productName.length * 1.5
      }},

      // 对于冒号，更加谨慎 - 只在明确是描述时才移除
      { pattern: /\s*:\s*(.+)$/, shouldRemove: (match: string, productName: string) => {
        const afterColon = match.split(':').slice(1).join(':').trim()
        // 当冒号后面是描述性文字时移除（降低长度要求）
        return afterColon.length > 20 && (
          afterColon.toLowerCase().includes('generate') ||
          afterColon.toLowerCase().includes('create') ||
          afterColon.toLowerCase().includes('build') ||
          afterColon.toLowerCase().includes('the best') ||
          afterColon.toLowerCase().includes('platform for') ||
          afterColon.toLowerCase().includes('collaborative') ||
          afterColon.toLowerCase().includes('design tool') ||
          afterColon.toLowerCase().includes('interface') ||
          afterColon.toLowerCase().includes('development') ||
          afterColon.toLowerCase().includes('solution') ||
          afterColon.toLowerCase().includes('software') ||
          afterColon.toLowerCase().includes('application') ||
          afterColon.toLowerCase().includes('service')
        )
      }}
    ]

    // 应用清理模式
    for (const { pattern, shouldRemove } of cleaningPatterns) {
      const match = cleanedTitle.match(pattern)
      if (match) {
        const beforeSeparator = cleanedTitle.split(pattern)[0].trim()
        if (beforeSeparator.length >= 2 && shouldRemove(match[0], beforeSeparator)) {
          cleanedTitle = beforeSeparator
        }
      }
    }

    // 移除其他分隔符后的内容（更保守）
    cleanedTitle = cleanedTitle
      .replace(/\s*·\s*.+$/, '') // 移除 "· 网站名称" 部分
      .replace(/\s*—\s*.+$/, '') // 移除 "— 网站名称" 部分
      .replace(/\s*–\s*.+$/, '') // 移除 "– 描述" 部分
      .trim()

    // 如果清理后的标题太短，尝试从原标题中提取更多信息
    if (cleanedTitle.length < 3) {
      // 尝试从域名生成名称作为回退
      return undefined // 让调用者使用 generateNameFromUrl
    }

    // 如果标题仍然太长，智能截取
    if (cleanedTitle.length > 60) {
      const words = cleanedTitle.split(' ')
      if (words.length > 6) {
        cleanedTitle = words.slice(0, 6).join(' ')
      } else if (cleanedTitle.length > 80) {
        cleanedTitle = cleanedTitle.substring(0, 77) + '...'
      }
    }

    return cleanedTitle || title
  }

  private extractTagline(content: string, description?: string): string | undefined {
    // 1. 尝试从主要标题下的副标题提取（优先简短的标语）
    const h1Matches = content.match(/<h1[^>]*>([\s\S]*?)<\/h1>/gi)
    if (h1Matches) {
      for (const h1Match of h1Matches) {
        // 查找 h1 后面的 p 标签或 h2 标签
        const h1Index = content.indexOf(h1Match)
        const afterH1 = content.substring(h1Index + h1Match.length, h1Index + h1Match.length + 1000)

        const nextPMatch = afterH1.match(/<p[^>]*>([^<]+(?:<[^>]*>[^<]*)*)<\/p>/i)
        if (nextPMatch) {
          const pText = nextPMatch[1].replace(/<[^>]*>/g, '').trim()
          if (pText.length > 10 && pText.length < 120 &&
              !pText.includes('cookie') &&
              !pText.includes('privacy') &&
              !pText.toLowerCase().includes('sign up') &&
              !pText.toLowerCase().includes('learn more')) {
            return pText
          }
        }
      }
    }

    // 2. 尝试从主要标题下的副标题提取
    const h2Matches = content.match(/<h2[^>]*>([^<]+)<\/h2>/gi)
    if (h2Matches) {
      for (const h2Match of h2Matches.slice(0, 3)) {
        const h2Text = h2Match.replace(/<[^>]*>/g, '').trim()
        if (h2Text.length > 10 && h2Text.length < 100 &&
            !h2Text.toLowerCase().includes('menu') &&
            !h2Text.toLowerCase().includes('navigation') &&
            !h2Text.toLowerCase().includes('footer') &&
            !h2Text.toLowerCase().includes('products') &&
            !h2Text.toLowerCase().includes('solutions')) {
          return h2Text
        }
      }
    }

    // 3. 尝试从 hero section 的简短段落提取
    const heroMatches = [
      content.match(/<section[^>]*class=["\'][^"']*hero[^"']*["\'][^>]*>([\s\S]*?)<\/section>/i),
      content.match(/<div[^>]*class=["\'][^"']*hero[^"']*["\'][^>]*>([\s\S]*?)<\/div>/i)
    ]

    for (const heroMatch of heroMatches) {
      if (heroMatch) {
        const heroContent = heroMatch[1]
        const pMatches = heroContent.match(/<p[^>]*>([^<]+(?:<[^>]*>[^<]*)*)<\/p>/gi)
        if (pMatches) {
          for (const pMatch of pMatches.slice(0, 2)) {
            const pText = pMatch.replace(/<[^>]*>/g, '').trim()
            if (pText.length > 15 && pText.length < 120 &&
                !pText.includes('cookie') &&
                !pText.includes('privacy') &&
                !pText.toLowerCase().includes('sign up') &&
                !pText.toLowerCase().includes('learn more') &&
                !pText.toLowerCase().includes('get started')) {
              return pText
            }
          }
        }
      }
    }

    // 4. 尝试使用简短的 meta description
    if (description && description.length > 10 && description.length < 100) {
      return description
    }

    // 5. 尝试从 og:description 提取
    const ogDescMatch = content.match(/<meta[^>]*property=["\']og:description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (ogDescMatch) {
      const ogDesc = ogDescMatch[1].trim()
      if (ogDesc.length > 10 && ogDesc.length < 120 && ogDesc !== description) {
        return ogDesc
      }
    }

    // 6. 从较长的 description 中提取第一句话
    if (description && description.length > 100) {
      const firstSentence = description.split(/[.!?]/)[0].trim()
      if (firstSentence.length > 15 && firstSentence.length < 100) {
        return firstSentence + '.'
      }
    }

    // 7. 最后回退：使用截断的 description
    if (description && description.length > 10) {
      return description.length > 100 ? description.substring(0, 97) + '...' : description
    }

    return undefined
  }

  private extractFavicon(content: string, url: string): string | undefined {
    const baseUrl = new URL(url).origin

    // 1. 尝试从 link rel="icon" 提取
    const iconMatches = [
      // 标准 favicon
      content.match(/<link[^>]*rel=["\'](?:icon|shortcut icon)["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i),
      // Apple touch icon
      content.match(/<link[^>]*rel=["\']apple-touch-icon["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i),
      // 其他图标格式
      content.match(/<link[^>]*rel=["\']icon["\'][^>]*type=["\']image\/[^"']*["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i)
    ]

    for (const match of iconMatches) {
      if (match && match[1]) {
        const iconUrl = match[1]
        // 处理相对路径
        if (iconUrl.startsWith('//')) {
          return `https:${iconUrl}`
        } else if (iconUrl.startsWith('/')) {
          return `${baseUrl}${iconUrl}`
        } else if (iconUrl.startsWith('http')) {
          return iconUrl
        } else {
          return `${baseUrl}/${iconUrl}`
        }
      }
    }

    // 2. 回退到默认 favicon 路径
    return `${baseUrl}/favicon.ico`
  }

  private extractOgImage(content: string, jinaData?: any): string | undefined {
    // 1. 尝试从 og:image 提取
    const ogImageMatch = content.match(/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (ogImageMatch) {
      return ogImageMatch[1]
    }

    // 2. 尝试从 twitter:image 提取
    const twitterImageMatch = content.match(/<meta[^>]*name=["\']twitter:image["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
    if (twitterImageMatch) {
      return twitterImageMatch[1]
    }

    // 3. 尝试从第一个有意义的图片提取（避免小图标）
    const imgMatches = content.match(/<img[^>]*src=["\']([^"']+)["\'][^>]*>/gi)
    if (imgMatches) {
      for (const imgMatch of imgMatches) {
        const srcMatch = imgMatch.match(/src=["\']([^"']+)["\']/)
        if (srcMatch) {
          const src = srcMatch[1]
          // 过滤掉小图标和明显的装饰性图片
          if (!src.includes('icon') &&
              !src.includes('logo') &&
              !src.includes('favicon') &&
              !src.includes('sprite') &&
              !src.includes('pixel') &&
              !src.endsWith('.svg') &&
              (src.includes('screenshot') ||
               src.includes('preview') ||
               src.includes('hero') ||
               src.includes('banner') ||
               imgMatch.includes('width') && !imgMatch.includes('width="16"') && !imgMatch.includes('width="32"'))) {
            return src
          }
        }
      }
    }

    return undefined
  }

  private generateNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
      const domain = urlObj.hostname.replace('www.', '')
      const domainParts = domain.split('.')
      return domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')
    } catch {
      return 'Unknown Product'
    }
  }
}
