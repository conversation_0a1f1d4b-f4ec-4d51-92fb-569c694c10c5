/**
 * 网站配置文件
 * 
 * 此文件集中管理网站的各种配置，包括：
 * - 首页 Highlight 组件的产品 ID 设置
 * - 集合页面的产品分类和 ID 设置
 * - 网站元数据和其他全局设置
 * - 产品徽章（icon badge）配置
 * 
 * 修改此文件可以快速更新网站内容，无需修改多个组件文件
 */

// 网站元数据配置
export const siteConfig = {
  name: "Introducing.day",
  description: "Discover the Latest Product Launches",
  url: "https://introducing.day",
  // 可以添加其他全局设置，如社交媒体链接、联系信息等
  socialLinks: {
    twitter: "https://twitter.com/introducing_day",
    github: "https://github.com/introducing-day",
  },
}

// Define the structure for productBadgesConfig
interface ProductBadgesConfigType {
  verified: number[];
  sponsor: number[];
  featured: number[];
  trusted: number[];
  new: number[];
  // Add other potential badge keys here if they exist or might be added
}

// Define a type for the keys, now based on the interface
export type BadgeKey = keyof ProductBadgesConfigType;

// 产品徽章配置 - 指定哪些产品显示特殊徽章
export const productBadgesConfig: ProductBadgesConfigType = {
  // 已验证产品
  verified: [1, 3, 16],
  
  // 赞助商产品
  sponsor: [],
  
  // 编辑推荐产品
  // editorPick: [/* product IDs */],
  // 新上线产品
  // newLaunch: [/* product IDs */],
  // 精选产品
  featured: [4, 5],
  
  // 可信产品
  trusted: [6, 7, 8, 9],
  
  // 新产品
  new: [10, 11, 17]
}

// Sidebar 内容配置
export const sidebarContentConfig = {
  sponsoredProductId: null, // Example: 10 (set to a specific product ID or null)
  featuredProductIds: [1, 5, 8, 16,2,3,4,6],   // Example: [1, 5, 8] (array of product IDs)
};

// 首页 Highlight 组件的产品 ID 设置
export const highlightConfig = {
  // 特色产品 (Featured) - 使用 BigCard 组件显示
  featuredIds: [16, 1, 3, 4, 5, 6],
  
  // 值得关注 (Worth Checking) - 使用 SmallCard 组件显示
  secondaryIds: [7, 8, 9],
  
  // 快速浏览 (Quick Picks) - 使用 TinyCard 组件显示
  tertiaryIds: [10, 11, 12, 13, 14, 17],
}

// 集合页面配置
export const collectionsConfig = [
  {
    title: "AI Tools",
    subtitle: "The best AI-powered tools for productivity",
    slug: "ai-tools",
    description:
      "A curated collection of the most innovative AI-powered tools that can help boost your productivity and streamline your workflow.",
    productIds: [6, 7, 16, 8, 9, 11],
  },
  {
    title: "Design Tools",
    subtitle: "Tools for designers and creative professionals",
    slug: "design-tools",
    description:
      "Essential tools for designers and creative professionals to create stunning visuals, illustrations, and user interfaces.",
    productIds: [1, 2, 3],
  },
  {
    title: "Productivity Apps",
    subtitle: "Boost your workflow with these productivity tools",
    slug: "productivity-apps",
    description:
      "Applications designed to help you stay organized, manage your time effectively, and get more done in less time.",
    productIds: [2, 22, 25, 11],
  },
  {
    title: "Developer Tools",
    subtitle: "Essential tools for software developers",
    slug: "developer-tools",
    description:
      "Tools that make software development faster, easier, and more efficient for developers of all skill levels.",
    productIds: [5, 10, 14, 16],
  },
  {
    title: "Marketing Tools",
    subtitle: "Tools to grow your audience and reach",
    slug: "marketing-tools",
    description:
      "Marketing solutions to help you reach your target audience, grow your brand, and measure your success.",
    productIds: [13, 22, 25],
  },
  {
    title: "Remote Work",
    subtitle: "Tools for distributed teams and remote work",
    slug: "remote-work",
    description: "Applications and services that make remote work and collaboration easier for distributed teams.",
    productIds: [11, 13, 2],
  },
]

// 其他可能的配置项
export const featuredCategories = [
  "AI",
  "Design",
  "Productivity",
  "Development",
  "Marketing",
]

// 网站导航配置
export const navigationConfig = {
  mainNav: [
    { title: "Home", href: "/" },
    { title: "Collections", href: "/collections" },
    { title: "About", href: "/about" },
  ],
}
