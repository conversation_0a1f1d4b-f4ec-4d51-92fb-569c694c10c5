export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: number
          name: string
          tagline: string
          logo_url: string | null
          cover_image_url: string | null
          category: string
          tags: string[] | null
          description: string | null
          url: string | null
          created_at: string
          updated_at: string
          slug: string | null
        }
        Insert: {
          id?: number
          name: string
          tagline: string
          logo_url?: string | null
          cover_image_url?: string | null
          category: string
          tags?: string[] | null
          description?: string | null
          url?: string | null
          created_at?: string
          updated_at?: string
          slug?: string | null
        }
        Update: {
          id?: number
          name?: string
          tagline?: string
          logo_url?: string | null
          cover_image_url?: string | null
          category?: string
          tags?: string[] | null
          description?: string | null
          url?: string | null
          created_at?: string
          updated_at?: string
          slug?: string | null
        }
      }
      product_reviews: {
        Row: {
          id: number
          name: string
          tagline: string
          logo_url: string | null
          logo: string | null
          cover_image_url: string | null
          cover_image: string | null
          category: string
          tags: string[] | null
          description: string | null
          url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          name: string
          tagline: string
          logo_url?: string | null
          logo?: string | null
          cover_image_url?: string | null
          cover_image?: string | null
          category: string
          tags?: string[] | null
          description?: string | null
          url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          name?: string
          tagline?: string
          logo_url?: string | null
          logo?: string | null
          cover_image_url?: string | null
          cover_image?: string | null
          category?: string
          tags?: string[] | null
          description?: string | null
          url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      product_schedules: {
        Row: {
          id: number
          name: string
          tagline: string
          logo: string | null
          cover_image: string | null
          category: string
          tags: string[] | null
          description: string | null
          url: string | null
          created_at: string
          updated_at: string
          scheduled_at: string
          product_review_id: number
          approved_by: string | null
        }
        Insert: {
          id?: number
          name: string
          tagline: string
          logo?: string | null
          cover_image?: string | null
          category: string
          tags?: string[] | null
          description?: string | null
          url?: string | null
          created_at?: string
          updated_at?: string
          scheduled_at: string
          product_review_id: number
          approved_by?: string | null
        }
        Update: {
          id?: number
          name?: string
          tagline?: string
          logo?: string | null
          cover_image?: string | null
          category?: string
          tags?: string[] | null
          description?: string | null
          url?: string | null
          created_at?: string
          updated_at?: string
          scheduled_at?: string
          product_review_id?: number
          approved_by?: string | null
        }
      }
      product_votes: {
        Row: {
          id: number
          product_id: number
          upvotes: number
          downvotes: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          product_id: number
          upvotes?: number
          downvotes?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          product_id?: number
          upvotes?: number
          downvotes?: number
          created_at?: string
          updated_at?: string
        }
      }
      user_votes: {
        Row: {
          id: number
          product_id: number
          user_id: string
          vote_type: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          product_id: number
          user_id: string
          vote_type?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          product_id?: number
          user_id?: string
          vote_type?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// 应用中使用的产品类型
export interface SupabaseProduct {
  id: number
  name: string
  tagline: string
  logo_url: string | null
  cover_image_url: string | null
  category: string
  tags: string[] | null
  description: string | null
  url: string | null
  created_at: string
  updated_at: string
  slug: string | null
}

// 将 Supabase 数据模型转换为应用数据模型的辅助函数
export function mapSupabaseProduct(product: SupabaseProduct) {
  return {
    id: product.id,
    name: product.name,
    tagline: product.tagline,
    logoUrl: product.logo_url || "",
    coverImageUrl: product.cover_image_url || "",
    category: product.category,
    tags: product.tags || [],
    description: product.description || undefined,
    url: product.url || undefined,
    createdAt: product.created_at,
    updatedAt: product.updated_at,
    slug: product.slug || undefined,
  }
}

// 应用中使用的产品类型
export interface Product {
  id: number
  name: string
  tagline: string
  logoUrl: string
  coverImageUrl: string
  category: string
  tags: string[]
  createdAt: string
  updatedAt: string
  description?: string
  url?: string
  slug?: string
  upvotes?: number
  downvotes?: number
}
