import type { Product } from "@/lib/supabase/types"

// This file contains only client-side database functions
// For server-side functions, import directly from "./db-server"

// 客户端版本的 getProductsByIds 函数
export const getProductsByIdsClient = async (ids: number[]): Promise<Product[]> => {
  if (!ids || ids.length === 0) {
    return []
  }

  try {
    const response = await fetch(`/api/products/by-ids?ids=${ids.join(",")}`)
    if (!response.ok) {
      throw new Error(`Error fetching products: ${response.statusText}`)
    }
    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching products by IDs:", error)
    return []
  }
}

// 客户端版本的 getProductsByCategory 函数
export const getProductsByCategoryClient = async (category: string): Promise<Product[]> => {
  try {
    const response = await fetch(`/api/products/by-category?category=${encodeURIComponent(category)}`)
    if (!response.ok) {
      throw new Error(`Error fetching products by category: ${response.statusText}`)
    }
    const data = await response.json()
    return data
  } catch (error) {
    console.error(`Error fetching products by category ${category}:`, error)
    return []
  }
}

// 客户端版本的 searchProducts 函数
export const searchProductsClient = async (query: string): Promise<Product[]> => {
  try {
    const response = await fetch(`/api/products/search?query=${encodeURIComponent(query)}`)
    if (!response.ok) {
      throw new Error(`Error searching products: ${response.statusText}`)
    }
    const data = await response.json()
    return data
  } catch (error) {
    console.error(`Error searching products for "${query}":`, error)
    return []
  }
}

// 客户端版本的产品数据预加载函数
export async function prefetchProductDataClient(id: string | number): Promise<void> {
  // 这个函数是为了在客户端使用的，它不会抛出错误
  try {
    // 在客户端，我们只预加载路由，不预加载数据
    // 这是因为客户端的 Supabase 客户端没有足够的权限来执行某些查询
    // 不执行实际的数据获取，避免客户端错误
  } catch (error) {
    // 静默处理错误
  }
}